@import "@/styles/variables.less";
page {
    height: 100%;
    background-color: #fff;
    font-size: 28px;
    line-height: 1.5;
}

.primary-btn {
    background-color: @primaryColor;
    color: #fff;
    border-radius: 100px;
    padding: 24px;
}


.total {
    background: #4b5c10;
    border-radius: 60px;
    box-shadow: 0 3px 8px 0 rgba(255, 255, 255, 0.4);
    display: flex;
    // justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    position: fixed;
    bottom: 24px;
    margin: 24px;
    padding: 24px;
    box-sizing: border-box;
    left: 0;
    right: 0;
    z-index: 2;
    color: #fff;
    &-left {
        display: flex;
        align-items: center;

        .icon {
            border-right: 1px solid rgba(255, 255, 255, 0.8);
            flex: 0.8;
            padding: 0 16px;
        }
        .text {
            margin-left: 16px;
        }
    }
}

