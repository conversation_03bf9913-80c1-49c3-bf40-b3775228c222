@primaryColor: #4b5c10; // 主要颜色
@secondaryColor: #a14e42; // 次要颜色

@base-space: 8px;
@margin-base: 16px;
@margin-small: @margin-base - @base-space;
@margin-large: @margin-base + @base-space;
@gray-8:#c8c8c8;
@padding-base: 16px;
@padding-small: @padding-base - @base-space;
@padding-large: @padding-base + @base-space;

.padding-variant(@name, @space) {
    body .padding-@{name} {
        padding: @space;

        &-left {
            padding-left: @space;
        }

        &-right {
            padding-right: @space;
        }

        &-bottom {
            padding-bottom: @space;
        }

        &-top {
            padding-top: @space;
        }

        &-top-bottom {
            padding-top: @space;
            padding-bottom: @space;
        }

        &-left-right {
            padding-left: @space;
            padding-right: @space;
        }
    }
}

.margin-variant(@name, @space) {
    body .margin-@{name} {
        margin: @space;

        &-left {
            margin-left: @space;
        }

        &-right {
            margin-right: @space;
        }

        &-bottom {
            margin-bottom: @space;
        }

        &-top {
            margin-top: @space;
        }

        &-top-bottom {
            margin-top: @space;
            margin-bottom: @space;
        }

        &-left-right {
            margin-left: @space;
            margin-right: @space;
        }
    }
}

.padding-variant(base, @padding-base);
.padding-variant(small, @padding-small);
.padding-variant(large, @padding-large);

.padding-none {
    padding: 0;

    &-bottom {
        padding-bottom: 0 !important;
    }

    &-left {
        padding-left: 0 !important;
    }

    &-right {
        padding-right: 0 !important;
    }

    &-top {
        padding-top: 0 !important;
    }
}

.margin-none {
    margin: 0;

    &-bottom {
        margin-bottom: 0 !important;
    }

    &-left {
        margin-left: 0 !important;
    }

    &-right {
        margin-right: 0 !important;
    }

    &-top {
        margin-top: 0 !important;
    }
}

.margin-variant(base, @margin-base);
.margin-variant(small, @margin-small);
.margin-variant(large, @margin-large);

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.title-text {
    font-size: 16px;
    font-weight: 600;
}

 