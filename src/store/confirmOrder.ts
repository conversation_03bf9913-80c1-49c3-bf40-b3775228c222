import { observable, runInAction, toJS } from "mobx";
import { MiniObject } from "../type/common";

const confirmOrderStore = observable({
    address: {},
    freightType: '1',
    initFreightType: 0,
    coupon: {},
    time: '',
    setAddress(data: MiniObject) {
        runInAction(() => {
            this.address = data;
        });
    },
    setFreightType(val) {
        runInAction(() => {
            this.freightType = val;
        })
    },
    setInitFreightType(val) {
        runInAction(() => {
            this.initFreightType = val;
        })
    },
    getAddress() {
        return toJS(this.address);
    },
    setCoupon(data: MiniObject) {
        runInAction(() => {
            this.coupon = data;
        });
    },
    getCoupon() {
        return toJS(this.coupon);
    },
    setTime(time: any) {
        runInAction(() => {
            this.time = time;
        })
    },
    getTime() {
        return toJS(this.time);
    },
});

export default confirmOrderStore;