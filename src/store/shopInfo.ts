import { observable, runInAction, toJS } from "mobx";
import { MiniObject } from "../type/common";
import api from "../util/api";

const shopStore = observable({
    shopInfo: {},
    setShopInfo(data: MiniObject) {
        runInAction(() => {
            this.shopInfo = data;
        });
    },
    init() {
        api.getShopLocation({
            type: 'get',
            data: { shopId: 1 },
        }).then((data) => {
            this.setShopInfo(data as MiniObject);
        })
    },
});

export default shopStore;