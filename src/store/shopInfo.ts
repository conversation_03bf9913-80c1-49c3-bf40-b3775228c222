import { observable, runInAction, toJS } from "mobx";
import { MiniObject } from "../type/common";
import api from "../util/api";

const shopStore = observable({
    shopInfo: {},
    shopList: [],
    selectedShop: {},
    setShopInfo(data: MiniObject) {
        runInAction(() => {
            this.shopInfo = data;
        });
    },
    setShopList(data: MiniObject[]) {
        runInAction(() => {
            this.shopList = data;
        });
    },
    setSelectedShop(data: MiniObject) {
        runInAction(() => {
            this.selectedShop = data;
            this.shopInfo = data; // 同时更新当前店铺信息
        });
    },
    getShopList() {
        return toJS(this.shopList);
    },
    getSelectedShop() {
        return toJS(this.selectedShop);
    },
    init() {
        api.getShopLocation({
            type: 'get',
            data: { shopId: 1 },
        }).then((data) => {
            this.setShopInfo(data as MiniObject);
        })
    },
    // 获取店铺列表
    loadShopList() {
        return api.getShopList({
            type: 'get',
            data: {},
        }).then((data) => {
            this.setShopList(data as MiniObject[]);
            return data;
        })
    },
});

export default shopStore;