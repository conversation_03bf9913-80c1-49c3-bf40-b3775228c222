import { navigateTo, navigateBack, redirectTo, switchTab, reLaunch } from "@tarojs/taro";
import { getStorage } from "./storage";
import Taro from "@tarojs/taro";
import confirmOrderStore from "@/store/confirmOrder";
import userStore from "@/store/user";

// 记录当前是否打开了登录页面
let isCurrentGoLogin = false;

const isPrevent = (url: string) => {
    console.log("url:", url);
    if (isCurrentGoLogin) {
        console.log("阻止短时间内重复打开登录页的行为");
        return true;
    }
    if (url.indexOf("pages/login/index") > -1) {
        console.log("当前要打开的是登录页");
        isCurrentGoLogin = true;
        setTimeout(() => {
            isCurrentGoLogin = false;
        }, 2000);
    }
    return false;
};

export function MiniNavigateTo(option: any) {
    if (!userStore.getUser()?.userId && !option.unNeedLogin) {
        navigateTo({
            url: "/pages/login/index"
        });
        return
    }
    if (isPrevent(option.url)) return;
    return navigateTo(option);
}

export function MiniBack(option?: any) {
    return navigateBack(option);
}

export function MiniRedirectTo(option: any) {
    if (isPrevent(option.url)) return;
    return redirectTo(option);
}

export function MiniSwitchTab(option: any) {
    return switchTab(option);
}

export function MiniReLaunch(option: any) {
    return reLaunch(option);
}

export const go = row => {
    var url = row.url;
    if (!url) {
        return;
    }

    // 替换装修平台商品链接地址为门店分销的
    url = url.replace("pages/item/item", "pages/goods_detail");
    if (url == "/pages/design/custom?id=" + getStorage("indexPageId")) {
        MiniSwitchTab({
            url: "/pages/design/index?id=" + getStorage("indexPageId"),
        });
    } else {
        MiniNavigateTo({
            url: url,
        });
    }
};

/**
 * 回退到指定url页面
 * @param url
 * @returns
 */
export const MiniBackToURL = (url: string) => {
    const urls = Taro.getCurrentPages().map(item => item.route);
    const delta = urls.length - 1 - urls.indexOf(url);
    console.log("回退页面数:", delta);
    return navigateBack({ delta });
};
