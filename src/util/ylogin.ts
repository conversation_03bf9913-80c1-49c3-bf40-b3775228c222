
import Taro from "@tarojs/taro";
import { getStorage, setStorage } from "./storage";
import MiniToast from "./toast";
import { IS_LOGIN } from "./constant";
import api from "./api";
import userStore from "@/store/user";
import { MiniObject } from "src/type/common";
export function loginCheck(userInfo?: MiniObject) {
    return new Promise((resolve, reject) => {
        Taro.login({
            success: function (res) {
                if (res.code) {
                    api.login({
                        data: userInfo ? {
                            code: res.code,
                            userInfo: userInfo
                        } : { code: res.code },
                        method: 'POST',
                        showError: false
                    })
                        .then(data => {
                            setStorage('token', data.token);
                            userStore.setUser(data.userInfo);
                            if (data) {
                                resolve(data);
                                //返回回调函数
                            } else {
                                reject(Error("login fail"));
                            }
                        })
                        .catch(err => {
                            reject(err);
                        });
                } else {
                    reject(res);
                }
            },
            fail: function (res) {
                reject(res);
            },
        });
    });
}

export const reLogin = () => {
    MiniToast.normal("重新登录中", "loading");
    return loginCheck().then(() => {
    });
};
export const localLoginMask = () => {
    return getStorage(IS_LOGIN) === 1;
};
// module.exports = {
//     loginCheck: loginCheck,
//     reLogin: reLogin
// }
