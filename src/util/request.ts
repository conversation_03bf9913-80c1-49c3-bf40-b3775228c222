/**
 * 公共请求封装
*/

import Taro from "@tarojs/taro";
import { MiniObject } from "src/type/common";
// import getHeader from "./server/header";
import { getStorage } from "./storage";
import { USER_COOKIE } from "./constant";
import MiniToast from './toast'
import { jumpLogin } from "./common";
import userStore from "@/store/user";
const getHeader = params => {
    let sessionId = getStorage(USER_COOKIE);;
    let header = {};
    if (sessionId) {
        sessionId = "msid=" + sessionId;
        header = {
            "Content-Type": params.contentType || "application/json",
            Cookie: sessionId,
            FromSource: "wechatMini",
            "ngrok-skip-browser-warning": "any"
        };
    } else {
        header = {
            "Content-Type": params.contentType || "application/json", FromSource: "wechatMini",
            "ngrok-skip-browser-warning": "any"
        };
    }
    return header;
};
export interface requestParams {
    data: MiniObject;
    url: string;
    /**
     * showLoad
     * 是否需要展示loading，优化交互
     */
    showLoad?: boolean;
    /**
     * showError
     * 是否需要展示错误信息，默认展示
     */
    showError?: boolean;
    contentType?: string;
    /**
     * method: 请求方法
     */
    method?: keyof Method;
    /**
     * filterCheck
     * 兼容老的接口，因为后端没有对业务成功或失败的判断，而是直接返回数据
     */
    filterCheck?: boolean;
    /**
     * boolean
     * 是否默认跳转登录
     * default: 是
     */
    isDefaultGoLogin?: boolean;
}

interface Method {
    /** HTTP 请求 OPTIONS */
    OPTIONS;
    /** HTTP 请求 GET */
    GET;
    /** HTTP 请求 HEAD */
    HEAD;
    /** HTTP 请求 POST */
    POST;
    /** HTTP 请求 PUT */
    PUT;
    /** HTTP 请求 PATCH */
    PATCH;
    /** HTTP 请求 DELETE */
    DELETE;
    /** HTTP 请求 TRACE */
    TRACE;
    /** HTTP 请求 CONNECT */
    CONNECT;
}

export interface resolveData {
    code: number;
    data: any;
    msg: string;
    message?: string;
    errorMsg?: string;
}

export interface rejectData {
    error: string;
    message: string;
    path: string;
    status: number;
    timestamp: number;
    errorMsg?: string;
}


type requestFun = (options: requestParams) => Promise<any>;

const request: requestFun = options => {
    /**
     * 只要成功接收到服务器返回，无论 statusCode 是多少，都会进入 success 回调。请开发者根据业务逻辑对返回值进行判断.
     *
     * https://developers.weixin.qq.com/miniprogram/dev/framework/ability/network.html#%E5%9B%9E%E8%B0%83%E5%87%BD%E6%95%B0
     * @param status
     */
    const validateStatus = function validateStatus(status) {
        return (status >= 200 && status < 300) || status === 304;
    };
    const loginInterceptor = function (resultData, isDefaultGoLogin) {
        if (!isDefaultGoLogin) {
            return false
        }
        if (
            resultData?.message === "账号处于非登录状态" ||
            resultData?.message === "用户未登录" ||
            resultData?.errorMsg === "用户未登录" ||
            resultData?.message === "未登录, 请登录" ||
            resultData.code === -1001
        ) {
            jumpLogin();
            return true;
        }
        return false;
    };
    const toastErrorMessage = resultData => {
        MiniToast.info(resultData.message || resultData.errorMsg || "请求异常");
    };
    return new Promise((resolve, reject) => {
        const { showError = true, method = "GET", isDefaultGoLogin = true } = options;
        const _header = getHeader(options);
        if (options.showLoad) {
            MiniToast.showLoading("加载中");
        }
        const userId = userStore.getUser().userId;
        Taro.request({
            ...options,
            header: _header,
            data: userId ? {
                ...options.data,
                userId
            } : options.data
            ,
            method: method,
            success: (result: Taro.request.SuccessCallbackResult<resolveData>) => {
                // console.log("result:", result)
                if (options.showLoad) {
                    MiniToast.hideLoading();
                }
                const { statusCode, data: resultData } = result;
                if (loginInterceptor(resultData, isDefaultGoLogin)) {
                    reject(resultData);
                    return;
                }
                if (resultData.code === 200) {
                    resolve(resultData.data);
                } else {
                    showError && toastErrorMessage(resultData);
                    reject(result);
                }
            },
            fail: err => {
                console.log(err)
                if (options.showLoad) {
                    MiniToast.hideLoading();
                }
                showError && MiniToast.info(err.errMsg || "请求异常");
                reject(err);
            },
        });
    });
};

export default request;
