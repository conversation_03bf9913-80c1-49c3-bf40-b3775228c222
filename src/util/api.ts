import { MiniObject } from "src/type/common";
import request, { requestParams } from "./request";
// const host = "https://mock.apifox.com/m1/4043600-0-default";
// const host = "http://**************";
// const host = "https://www.7riverlight.com";
// const host = "https://wxapitest.7riverlight.com";
const host = "https://5937-122-231-144-56.ngrok-free.app";
// const host = "https://8101-240e-391-cde-d530-5420-25a7-711f-4b31.ngrok-free.app";
// const host = "https://76d2-240e-391-cdf-e390-7978-41c4-16cd-1eb7.ngrok-free.app"
// const host = "https://f29b-240e-391-cd5-beb0-1170-ff79-5309-4dc2.ngrok-free.app"
// const host = "https://649d-112-10-211-56.ngrok-free.app";
// const host = "https://98c8-112-10-201-37.ngrok-free.app";
// const host = "https://595a-240e-390-9e7-d330-680f-b853-12de-1dc0.ngrok-free.app";
type wxRequestParams = Omit<requestParams, "url">;

const wxRequest = (params: wxRequestParams, url: string) => {
    return request({ ...params, url: url });
};

const preOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/checkout")
}

const preShopOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/shop/checkout")
}

//确认订单
const submitOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/submit")
}

//确认订单1
const submitMailOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/shop/submit")
}

const getWxPayParams = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/prepay")
}

// c/wx/order/combinePrepay
const getWxShopPayParams = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/combinePrepay")
}

// 登录
const login = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/auth/login_by_weixin")
}

const getUserAddress = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/address/list")
}

// 订单相关
const getOrderdetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/detail")
}

const refundOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/refund")
}

const cancelOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/cancel")
}

const confirmOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/confirm")
}

const delOrder = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/delete")
}

const getOrderList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/list")
}

const payOrderByBalance = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/balance/pay")
}

const getSelectCouponList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/coupon/selectlist")
}

// 收货地址相关

const getRegion = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/region/list")
}

const saveAddress = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/address/save")
}

const getaddressDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/address/detail")
}

const getCategory = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/goods/category/L2")
}

const getGoodsList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/goods/listV2")
}

const getDelete = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/address/delete")
}

const getShopLocation = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/shop/getShopLocation")
}

// 获取店铺列表
const getShopList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/shop/getShopListByLatAndLng")
}

// 购物车相关
const cartAdd = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/add")
}
const cartUpdate = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/update")
}

const goodsUpdate = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/fastAddNumber")
}

const clearCarts = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/deleteAll")
}

const getCarts = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/index")
}
const getShopCarts = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/cart/shop/index")
}

const getPointExchange = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/point/exchange/list")
}

const exchangeCoupons = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/point/exchange/submit")
}

const getPointHisList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/point/his/list")
}

const getRechargeConfig = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/recharge/config/list")
}

const preRecharge = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/recharge/preRecharge")
}

const getBalanceHisList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/balance/list")
}

const getRechargeHisList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/recharge/list")
}

const getUserDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/user/detail")
}

const getAboutUs = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/pageConfig/aboutUs")
}

// 关于我们 文案
const getAboutUsDesc = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/pageConfig/aboutUs/desc")
}

const updateUserInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/user/updateUserInfo")
}

const getNewUserInfo = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/user/userInfo")
}

const queryQrCode = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/existUnTakeMeal")
}

// 优惠券相关

const getCouponList = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/coupon/mylist")
}

const getShopStatus = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/shop/getOpenStatus")
}

const getMealStatus = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/isFinishTakeMeal")
}

const getCouponDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + '/wx/coupon/couponUserDetail')
}

const getGoodDetail = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/goods/detail");
}

const searchGoods = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/goods/shop/search")
}

const payOrdersByBalance = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/combine/balance/pay")
}

const getWaybillRoute = (params: wxRequestParams) => {
    return wxRequest(params, host + "/wx/order/getWaybillRoute")
}

// const getShopList = ()
const api: MiniObject = {
    preShopOrder,
    searchGoods,
    getGoodDetail,
    login,
    submitOrder,
    getWxPayParams,
    preOrder,
    getUserAddress,
    getOrderdetail,
    cancelOrder,
    saveAddress,
    getCategory,
    getGoodsList,
    cartAdd,
    getWxShopPayParams,
    payOrdersByBalance,
    goodsUpdate,
    cartUpdate,
    getCarts,
    getShopCarts,
    refundOrder,
    confirmOrder,
    delOrder,
    getOrderList,
    getRegion,
    getaddressDetail,
    getPointExchange,
    exchangeCoupons,
    getPointHisList,
    getBalanceHisList,
    getRechargeConfig,
    getRechargeHisList,
    preRecharge,
    getUserDetail,
    payOrderByBalance,
    getCouponList,
    getSelectCouponList,
    clearCarts,
    getAboutUs,
    getAboutUsDesc,
    updateUserInfo,
    getNewUserInfo,
    submitMailOrder,
    getDelete,
    queryQrCode,
    getShopStatus,
    getMealStatus,
    getShopLocation,
    getShopList,
    getCouponDetail,
    getWaybillRoute
}
export default api