// @ts-nocheck
import React from "react";
import { View, Text } from "@tarojs/components";
import { Icon } from "@antmjs/vantui";
import { observer } from "mobx-react";
import shopStore from "@/store/shopInfo";
import { MiniNavigateTo } from "@/util/route";
import "./shop-selector.less";

interface ShopSelectorProps {
    onShopChange?: (shop: any) => void;
    className?: string;
}

const ShopSelector: React.FC<ShopSelectorProps> = observer(({ onShopChange, className }) => {
    const selectedShop = shopStore.getSelectedShop();
    const currentShop = Object.keys(selectedShop).length > 0 ? selectedShop : shopStore.shopInfo;

    const handleSelectShop = () => {
        MiniNavigateTo({
            url: '/pages/shop/select-shop/index'
        });
    };

    return (
        <View className={`shop-selector ${className || ''}`} onClick={handleSelectShop}>
            <View className="shop-info">
                <Icon name="location-o" size="16" color="#ff6b35" />
                <View className="shop-details">
                    <Text className="shop-name">
                        {currentShop?.shopName || '请选择门店'}
                    </Text>
                    {currentShop?.address && (
                        <Text className="shop-address">
                            {currentShop.address}
                        </Text>
                    )}
                </View>
            </View>
            <Icon name="arrow" size="16" color="#999" />
        </View>
    );
});

export default ShopSelector;
