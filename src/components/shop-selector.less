.shop-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: background-color 0.2s;

  &:active {
    background-color: #f8f8f8;
  }

  .shop-info {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 8px;

    .shop-details {
      flex: 1;

      .shop-name {
        font-size: 28px;
        font-weight: 500;
        color: #333;
        display: block;
        margin-bottom: 2px;
      }

      .shop-address {
        font-size: 24px;
        color: #666;
        display: block;
        line-height: 1.3;
      }
    }
  }
}

/* 在购物车页面中的样式调整 */
.cart-page .shop-selector {
  margin: 0;
  border-radius: 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 在确认订单页面中的样式调整 */
.confirm-order .shop-selector {
  background: transparent;
  padding: 8px 0;
  margin: 0;

  .shop-info {
    .shop-details {
      .shop-name {
        color: white;
      }

      .shop-address {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}
