import React, { useCallback } from "react";
import { Picker, View } from "@tarojs/components";
import {
    PickerDateProps,
    PickerMultiSelectorProps,
    PickerRegionProps,
    PickerSelectorProps,
    PickerTimeProps,
} from "@tarojs/components/types/Picker";

type PickerOnChange = Partial<
    Pick<
        PickerMultiSelectorProps | PickerTimeProps | PickerDateProps | PickerRegionProps | PickerSelectorProps,
        "onChange"
    >
>;

type FormPickerProps = Omit<
    PickerMultiSelectorProps | PickerTimeProps | PickerDateProps | PickerRegionProps | PickerSelectorProps,
    "onChange"
> & {
    /**
     * 格式化选择后展示值
     * @param value 选择的数据
     */
    formatValueFn?: (value) => { formatValue: string; toSetValue: string };
    defaultText?: string
} & PickerOnChange;

export const FormPicker: React.FC<FormPickerProps> = props => {
    const innerOnChange = useCallback(e => {
        if (props.onChange) {
            props.onChange(e);
        }
    }, []);
    const { value, mode, formatValueFn } = props;
    const defaultFormatValue = useCallback(pickValue => {
        let buildValue = Object.create({});
        switch (mode) {
            case "selector":
                // 需要外部实现
                buildValue = formatValueFn && formatValueFn(pickValue);
                break;
            case "multiSelector":
                // 需要外部实现
                buildValue = formatValueFn && formatValueFn(pickValue);
                break;
            case "time":
                buildValue.formatValue = value;
                buildValue.toSetValue = value;
                break;
            case "date":
                buildValue.formatValue = value;
                buildValue.toSetValue = value;
                break;
            case "region":
                buildValue.formatValue = pickValue?.value?.join("-") || "省-市-区";
                buildValue.toSetValue = pickValue?.value;
                break;
        }
        return buildValue;
    }, [value]);
    const innerProps = Object.assign(props);
    // console.log("picker:", value, defaultFormatValue(value)?.formatValue)
    return (
        <Picker
            {...innerProps}
            onChange={innerOnChange}
            style={{ width: "100%" }}
            value={defaultFormatValue(value).toSetValue}
        >
            <View className="picker">
                {value ? (defaultFormatValue(value).formatValue || value) : <View className="input-placeholder">{props?.defaultText || '请选择'}</View>}
            </View>
        </Picker>
    );
};
