
import { View, Text, Image } from "@tarojs/components";

import "./goods-list.less";
export default ({ list }) => {
    return (
        <View className="shop-item-list">
            <View className="goods-list">
                {list.map(goodsItem => (
                    <View className="good-item">
                        <View className="good-img">
                            <Image className="good-img" src={goodsItem.picUrl} mode="scaleToFill" />
                        </View>
                        <View className="good-content">
                            <View className="good-content-text1">{goodsItem.goodsName}
                            </View>
                            <View className="good-content-text2">{goodsItem.specificationJoin}</View>
                        </View>
                        <View className="good-info">
                            <View className="good-price">¥{goodsItem.price}</View>
                            {/* {
                                goodsItem.discountFlag ?
                                    <View >
                                        <View className="good-price" style={{ textDecoration: 'line-through' }}>￥{goodsItem.price}</View>
                                        <View className="good-price" style={{ marginLeft: '8px', color: 'red' }}>¥{goodsItem.discountPrice}</View>
                                    </View>
                                    :
                                    <View className="good-price">￥{goodsItem.price}</View>

                            } */}
                            <View className="good-num">x{goodsItem.number}</View>
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};
