import { useEffect, useRef, useState } from "react";
import { FormPicker } from "./form-picker";
import { Picker, View } from "@tarojs/components";
import "./limit-time-picker.less"

export default ({ onChange, value }) => {
    const [start, setStart] = useState('10:30');
    const [end, setEnd] = useState<any>("20:00");
    const [cvalue, setCValue] = useState(value);
    const [showReal, setShowReal] = useState(false)
    const clickBol = useRef(false)

    const init = (val) => {
        let newStartTime = new Date();
        let hour = newStartTime.getHours();
        let min = newStartTime.getMinutes() + 30;
        if (min >= 60) {
            hour += 1;
            min = min - 60;
        }
        if (hour < 10) {
            //@ts-ignore
            hour = '0' + hour;
        }
        if (min < 10) {
            //@ts-ignore
            min = '0' + min;
        }
        if ((val === '立即取单' || !val)) {
            setCValue(hour + ":" + min)
        } else {
            setShowReal(true)
            setCValue(val)
        }
    }
    useEffect(() => {
        init(value)
    }, [value])
    return (
        <View style={{ width: '33%', display: 'flex', flexDirection: 'row', justifyContent: 'flex-end' }}
            onClick={() => {
                clickBol.current = true
                init(value)
            }}
        >
            <Picker
                mode="time"

                start={start}
                end={end}
                value={cvalue}
                onChange={(e) => {
                    // console.log("val:", e.detail.value, clickBol.current)
                    setCValue(e.detail.value)
                    onChange(e.detail.value)
                    setShowReal(true)
                }}
            >
                <View className="picker">
                    {cvalue && showReal ?
                        cvalue
                        :
                        <View className="input-placeholder">立即取单</View>}
                </View>
            </Picker>

        </View >

    )
}