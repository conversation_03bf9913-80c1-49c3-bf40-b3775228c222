import { View, Image, Text } from "@tarojs/components"
import "./good-item.less"
import { MiniNavigateTo } from "@/util/route"
import { url } from "inspector"
export default ({ goodItem, size }) => {
    return (<View className="good-item" onClick={() => {
        MiniNavigateTo({
            url: `/pages/mail/goods-detail/index?id=${goodItem.id}`,
            unNeedLogin: true
        })
    }}>
        <View className="good-item-img">
            <Image src={goodItem.picUrl} />
        </View>
        <View className="good-title">
            <Text className="gppd-title-text">{goodItem.name}</Text>
        </View>
        <View className="good-price">
            <Text className="gppd-price-text">¥{goodItem.retailPrice || 0.00}</Text>
        </View>
    </View>)
}