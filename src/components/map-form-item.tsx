import { View, Text } from "@tarojs/components"
import Taro, { requirePlugin, useDidShow } from "@tarojs/taro"
import React from "react"
const chooseLocation = requirePlugin('chooseLocation');
const MapFormItem: React.FC<{ value: any, onChange: (e: any) => void }> = (props) => {
    // console.log(props)
    const text = props?.value ? props.value.address : ''

    useDidShow(() => {
        const location = chooseLocation.getLocation(); // 如果点击确认选点按钮，则返回选点结果对象，否则返回null
        console.log("location:", props?.value)
    })

    return (<View
        style={{ height: '100%', width: '100%' }}
        onClick={() => {
            console.log(2222)
            Taro.chooseLocation({
                latitude: props?.value?.latitude,
                longitude: props?.value?.longitude,
                success(result) {
                    console.log("result:", result)
                    props.onChange({ ...result })
                },
                fail(res) {
                    console.log("res:", res)
                },
            })
            // return
            // const key = "EB7BZ-W7X6J-KL7FS-X7M53-K63S5-WTFLA";
            // const referer = "7 RIVERLIGHT";
            // const location = JSON.stringify({
            //     latitude: 39.89631551,
            //     longitude: 116.323459711
            // })
            // const category = "";
            // //@ts-ignore
            // wx.navigateTo({
            //     url: 'plugin://chooseLocation/index?key=' + key + '&referer=' + referer + '&location=' + location + '&category=' + category
            // });

        }}
    >
        {text || <Text style={{ color: '#666' }}>请选择地点</Text>}
    </View>)
}

export default MapFormItem