import { Component, PropsWithChildren } from 'react'
import { Provider } from 'mobx-react'

import counterStore from './store/counter'
import userStore from './store/user'
import './app.less'
import { loginCheck } from './util/ylogin'
import confirmOrderStore from './store/confirmOrder'
import shopStore from './store/shopInfo'

const store = {
    counterStore,
    userStore,
    confirmOrderStore
}

class App extends Component<PropsWithChildren> {
    componentDidMount() {
        loginCheck().then(res => {
            console.log(res)
        })
        shopStore.init();
    }

    componentDidShow() { }

    componentDidHide() { }

    // this.props.children 就是要渲染的页面
    render() {
        return (
            <Provider store={store}>
                {this.props.children}
            </Provider>
        )
    }
}

export default App
