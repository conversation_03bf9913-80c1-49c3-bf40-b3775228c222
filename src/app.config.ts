
export default defineAppConfig({
    pages: [
        "pages/demo/shop-demo/index",
        "pages/home/<USER>",
        "pages/mail/main/index",
        // 'pages/order/address/index',
        // "pages/container/index",
        // "pages/order/address/add-new-address/index",

        // "pages/mail/main/index",
        // "pages/my/coupon-detail/index",
        "pages/mail/search/index",
        "pages/mail/search-result/index",
        "pages/mail/confirm-order/index",
        "pages/mail/cart/index",
        // "pages/home/<USER>",
        "pages/my/setting/index",
        "pages/mail/goods-detail/index",
        "pages/container/index",
        "pages/my/coupon/index",
        "pages/my/coupon-detail/index",
        'pages/order/cashier/index',
        'pages/my/point/index',
        'pages/my/point-his/index',
        'pages/my/balance/index',
        'pages/my/balance-his/index',
        'pages/my/recharge-his/index',
        'pages/cart/cart',
        'pages/login/index',
        'pages/order/confirm-order/index',
        'pages/order/order-detail/index',
        'pages/order/address/index',
        'pages/order/address/add-address/index',
        'pages/about-us/index',
        'pages/order/select-coupon/index',
        'pages/order/order-logistics/index',
        'pages/shop/select-shop/index',
        // 'pages/demo/shop-demo/index',
    ],
    window: {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTitleText: 'WeChat',
        navigationBarTextStyle: 'black'
    },
    requiredPrivateInfos: ["chooseAddress", "chooseLocation", "getLocation"],
    permission: {
        "scope.userLocation": {
            desc: "你的位置信息将用于定位效果展示",
        },
        // "scope.getLocation": {
        //     desc: "获取位置"
        // }
    },
    enableShareAppMessage: true,// 分享好友
    enableShareTimeline: true,// 分享朋友圈
    plugins: {
        chooseLocation: {
            "version": "1.0.12",
            "provider": "wx76a9a06e5b4e693e"
        }
    }
})
