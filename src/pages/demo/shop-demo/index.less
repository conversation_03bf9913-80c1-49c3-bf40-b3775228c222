.shop-demo {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .demo-section {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-title {
      font-size: 32px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
      display: block;
    }

    .current-shop {
      .shop-info {
        .shop-name {
          font-size: 36px;
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
          display: block;
        }

        .shop-address {
          font-size: 28px;
          color: #666;
          margin-bottom: 4px;
          display: block;
          line-height: 1.4;
        }

        .shop-hours {
          font-size: 24px;
          color: #999;
          display: block;
        }
      }

      .no-shop {
        font-size: 14px;
        color: #999;
        text-align: center;
        padding: 20px;
        display: block;
      }
    }

    .demo-button {
      width: 100%;
      background: linear-gradient(135deg, #ff6b35, #f7931e);
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }

    .feature-list {
      .feature-item {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        margin-bottom: 8px;
        display: block;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 适配暗色模式 */
@media (prefers-color-scheme: dark) {
  .shop-demo {
    background-color: #1a1a1a;

    .demo-section {
      background: #2a2a2a;

      .section-title {
        color: #fff;
      }

      .current-shop {
        .shop-info {
          .shop-name {
            color: #fff;
          }

          .shop-address {
            color: #ccc;
          }

          .shop-hours {
            color: #999;
          }
        }

        .no-shop {
          color: #666;
        }
      }

      .feature-list {
        .feature-item {
          color: #ccc;
        }
      }
    }
  }
}
