import React from "react";
import { View, Text, Button } from "@tarojs/components";
import { observer } from "mobx-react";
import shopStore from "@/store/shopInfo";
import ShopSelector from "@/components/shop-selector";
import { MiniNavigateTo } from "@/util/route";
import "./index.less";

definePageConfig({
    navigationBarTitleText: '店铺选择演示'
});

const ShopDemo = observer(() => {
    const selectedShop = shopStore.getSelectedShop();
    const currentShop = Object.keys(selectedShop).length > 0 ? selectedShop : shopStore.shopInfo;

    const handleDirectNavigation = () => {
        MiniNavigateTo({
            url: '/pages/shop/select-shop/index'
        });
    };

    return (
        <View className="shop-demo">
            <View className="demo-section">
                <Text className="section-title">当前选择的店铺</Text>
                <View className="current-shop">
                    {currentShop?.shopName ? (
                        <View className="shop-info">
                            <Text className="shop-name">{currentShop.shopName}</Text>
                            <Text className="shop-address">{currentShop.address}</Text>
                            <Text className="shop-hours">{currentShop.businessHours || '营业时间未知'}</Text>
                        </View>
                    ) : (
                        <Text className="no-shop">暂未选择店铺</Text>
                    )}
                </View>
            </View>

            <View className="demo-section">
                <Text className="section-title">使用组件选择店铺</Text>
                <ShopSelector />
            </View>

            <View className="demo-section">
                <Text className="section-title">直接跳转选择页面</Text>
                <Button 
                    className="demo-button"
                    onClick={handleDirectNavigation}
                >
                    打开店铺选择页面
                </Button>
            </View>

            <View className="demo-section">
                <Text className="section-title">功能说明</Text>
                <View className="feature-list">
                    <Text className="feature-item">• 支持地图显示所有店铺位置</Text>
                    <Text className="feature-item">• 自动获取用户位置并计算距离</Text>
                    <Text className="feature-item">• 支持搜索店铺名称和地址</Text>
                    <Text className="feature-item">• 可以拨打店铺电话</Text>
                    <Text className="feature-item">• 可以打开地图导航到店铺</Text>
                    <Text className="feature-item">• 选择后自动返回上一页</Text>
                </View>
            </View>
        </View>
    );
});

export default ShopDemo;
