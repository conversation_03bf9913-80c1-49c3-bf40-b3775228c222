import { View, Text, Image } from "@tarojs/components"
import { MiniBack, MiniReLaunch } from "../../util/route";
import MiniToast from "../../util/toast";
import { loginCheck } from "../../util/ylogin";
import { Checkbox, Button } from "@antmjs/vantui";
import { useEffect, useState } from "react";
import './index.less'
definePageConfig({
    navigationBarTitleText: "登录",
});
const IconHomeDetailTop = "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-index-back.jpg";

export default () => {
    const [value, setValue] = useState(false);
    const bindGetUserInfo = (e) => {
        console.log(e);
        if (!value) return MiniToast.error("请阅读隐私协议");
        MiniToast.showLoading("登录中");
        loginCheck(e.detail.userInfo)
            .then((data) => {
                MiniBack()
            })
            .finally(() => {
                MiniToast.hideLoading();
            });
    }

    useEffect(() => {

    }, [])
    return (<View className="login">
        <Image
            className="app-icon"
            src={IconHomeDetailTop}
        />
        <Text style={{}}>7 RIVERLIGHT</Text>

        <View className="private-note">
            <Checkbox value={value} iconSize={24} onChange={e => setValue(e.detail)} checkedColor="#07c160" />
            我已阅读并同意{" "}
            <Button className="private-note-privacy" open-type="agreePrivacyAuthorization" onAgreePrivacyAuthorization={() => {
                // console.log('同意隐私授权');
                //@ts-ignore
                wx.openPrivacyContract({
                    success: () => { }, // 打开成功
                    fail: () => { }, // 打开失败
                    complete: () => { }
                })

            }}>隐私协议</Button>
        </View>
        <Button
            className="login-btn"
            color="#04c060"
            open-type="getUserInfo"
            onGetUserInfo={bindGetUserInfo}
        >
            一键快捷登录
        </Button>
    </View>)
}