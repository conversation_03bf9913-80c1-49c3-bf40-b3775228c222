@import "@/styles/variables.less";
.home-detail {
    background-color: #fff;
    width: 100%;
    position: relative;
    &-top {
        width: 100%;
        text-align: center;
        image {
          width: 600px;
          height: 413.3px;
        }
    }
    &-text {
        // https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/open_time.png
        // font-family: cursive;
        // font-style: italic;
        // text-align: center;
        // padding: 20px 0;
        // font-weight: 500;
        background-image: url(https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/open_time.png);
       background-position: center;
       height: 240px;
       background-repeat: no-repeat;
       background-size: contain;
    }
    &-center {
        background-size: contain;
        width: 100%;
        // height: 400px;
        background-image: url(https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-detail-bg.jpg);
        display: flex;
        justify-content: space-between;
        &-box {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            margin: 20px;
            width: 50%;
            overflow: hidden;
            background-color: rgba(0, 0, 0, 0);
            padding: 18px;
            box-sizing: border-box;
            font-family: cursive;
            &.left {
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3); /* 添加阴影效果 */
                transition: box-shadow 0.3s; /* 添加过渡效果 */
            }
            image {
                // transform: scale(0.6); /* 缩放图片为原始大小的50% */
                height: 200px;
                width: 100%;
                padding: 40px;
            }
            .text {
                font-size: 38px;
            }
        }
      
        .color-red {
            color: @secondaryColor;
        }
    }
    &-footer {
        width: 100%;
        padding: 50px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        &-img{
            max-width: 100%;
            max-height: 140px;
        }
    }
    &-card {
        border: 4px solid #4b5c10;
        border-radius: 20px;
        background-color: #fcfdfc;
        padding: 24px;
        margin: 24px;
        overflow: auto;
        > view:nth-child(2) {
            color: #2d2b2b;
            font-size: 32px;
            font-weight: 700;
        }
        > view:nth-child(3) {
            padding: 0;
            border: none;
            outline: none;
            background: #4b5c10;
            color: #fff;
            border-radius: 20px;
            width: 160px;
            line-height: 2;
            float: right;
            text-align: center;
        }
    }
    &-about {
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        padding: 24px;
        text-align: center;
        padding-bottom: calc(180px + env(safe-area-inset-bottom));
        > view {
            flex: 1;
        }
        image {
            flex: 1;
            height: 200px;
        }
    }
}
.order-code-btn{
    position: fixed;
    right: 60px;
    bottom: 300px;
    // border: 
    border-radius: 50%;
    height: 120px;
    width: 120px;
    // background-color: #eef60d;
    background-color: @primaryColor;
    box-shadow:  0px 0px 30px rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}
