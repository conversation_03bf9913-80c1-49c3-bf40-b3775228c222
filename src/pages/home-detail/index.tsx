/**
 * @description 首页详情
 * <AUTHOR>
 */
import { MiniNavigateTo } from "@/util/route";
import { Image, View } from "@tarojs/components";
import { Dialog } from '@antmjs/vantui'
import React, { useEffect, useRef, useState, useCallback } from "react";
import "./index.less";
import { observer } from "mobx-react";
import api from "@/util/api";
import userStore from "@/store/user";
import confirmOrderStore from "@/store/confirmOrder";
import { Popup } from "@antmjs/vantui";
import QrCodeModal from "./qrCode-modal";
import Taro, { useDidShow } from "@tarojs/taro";

const IconHomeDetailTop = "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-index-back.jpg";
const IconHomeDetailTopMini = "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-index-back-mini.jpg";
const IconHomeDetailFooter = "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-detail-footer.png";
const IconHomeDetailLeftHand = "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-detail-left-hand.jpg";
const IconHomeDetailRightHand = "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-detail-right-hand.png";
const IconHomeDetailAbout = "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/home-detail-about.jpg";
const Dialog_ = Dialog.createOnlyDialog()
export default observer(({ goOrder }) => {
    const [detail, setDetail] = useState({});
    const [showQrCode, setShowQrCode] = useState(false);
    const [qrcodeObj, setQrcodeObj] = useState({});
    const closeRef = useRef(false);
    const getUserDetail = () => {
        api.getUserDetail({ showError: false }).then(res => {
            setDetail(res);
        });
        api.queryQrCode({ showError: false, method: 'post' }).then(res => {
            if (res.multiUnTakeMeal) {
                console.log('222')
                setShowQrCode(true)
            }
            if (res.hasUnTakeMeal) {
                setShowQrCode(true)
                // setQrcodeObj()
            }
            setQrcodeObj(res);

        })
    };

    const getStatus = (fn) => {
        if (closeRef.current) {
            fn && fn();
            return
        }
        api.getShopStatus({
            data: {
                shopId: 1
            },
            method: 'get'
        }).then(res => {
            console.log(res)
            if (!res.open) {
                closeRef.current = true;
                Dialog_.alert({
                    title: '店铺未营业',
                    message: res.statusDesc,
                }).then((value) => {
                    console.log("value:", value)
                    fn && fn()
                }).catch((err) => {
                    console.log(err);
                })
            } else {
                fn && fn()
            }

        })
    }

    // useEffect(() => {
    //     userStore.getUser().userId && getUserDetail();
    // }, []);

    useDidShow(() => {
        // console.log("useDidShow")
        userStore.getUser().userId && getUserDetail();
    })

    return (
        <View className="home-detail"
        >
            <View>
                <View className="home-detail-top">
                    <Image src={IconHomeDetailTopMini} className="home-detail-top-img" />
                </View>
                <View className="home-detail-center">
                    <View
                        className="home-detail-center-box left"
                        onClick={() => {
                            getStatus(() => {
                                confirmOrderStore.setFreightType('1');
                                confirmOrderStore.setAddress({});
                                confirmOrderStore.setInitFreightType(1)
                                MiniNavigateTo({
                                    url: "/pages/cart/cart",
                                    unNeedLogin: true,
                                });
                            })

                        }}>
                        <View className="color-red text">Pick Up</View>
                        <Image src={IconHomeDetailLeftHand}></Image>
                        <View className="text">自取</View>
                    </View>
                    <View
                        className="home-detail-center-box"
                        onClick={() => {
                            getStatus(() => {
                                confirmOrderStore.setFreightType('0')
                                confirmOrderStore.setInitFreightType(0)
                                MiniNavigateTo({
                                    url: "/pages/order/address/index?type=select",
                                    unNeedLogin: true,
                                });
                            })
                        }}>
                        <View className="text">外送</View>
                        <Image src={IconHomeDetailRightHand} style={{ width: '90%', height: '144px', boxSizing: 'border-box' }}></Image>
                        <View className="color-red text">Delivery</View>
                    </View>

                </View>
                <View
                    className="home-detail-card"
                    style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                    onClick={() => {
                        // getStatus(() => {
                        // confirmOrderStore.setFreightType('3')
                        MiniNavigateTo({
                            url: "/pages/mail/main/index",
                            unNeedLogin: true,
                        });
                        // })

                    }}>
                    <View className="text">全国顺丰   SF EXPRESS</View>
                    <Image style={{ width: '40px', height: '40px' }} mode="aspectFill" src="https://www.sf-express.com/chn/_next/static/media/ic-black-logo.c86816fe.png" />
                </View>
                {/* <View className="home-detail-footer">
                    <Image src={IconHomeDetailFooter} className="home-detail-footer-img"></Image>
                </View> */}
            </View>
            <View>
                <View className="home-detail-card">
                    <View>会员卡</View>
                    <View>{detail?.membershipLevel || ""}</View>
                    <View></View>
                </View>
                <View
                    className="home-detail-card"
                    onClick={() => {
                        MiniNavigateTo({
                            url: "/pages/my/balance/index",
                        });
                    }}>
                    <View>储值余额</View>
                    <View>{detail?.balance || "0"}元</View>
                    <View>立即充值</View>
                </View>
                <View
                    className="home-detail-card"
                    onClick={() => {
                        MiniNavigateTo({
                            url: "/pages/my/point/index",
                        });
                    }}>
                    <View>积分</View>
                    <View>{detail?.points || "0"}分</View>
                    <View>积分兑换</View>
                </View>
            </View>
            <View
                className="home-detail-about"
                onClick={() => {
                    MiniNavigateTo({
                        url: "/pages/about-us/index",
                    });
                }}>
                <View>About Us</View>
                <Image src={IconHomeDetailAbout}></Image>
                <View>关于我们</View>
            </View>
            {
                showQrCode && <QrCodeModal data={qrcodeObj} go={goOrder} load={() => {
                    api.queryQrCode({ showError: false, method: 'post' }).then(res => {
                        if (res.multiUnTakeMeal) {
                            console.log('222')
                            setShowQrCode(true)
                        }
                        if (res.hasUnTakeMeal) {
                            setShowQrCode(true)
                            // setQrcodeObj()
                        }
                        setQrcodeObj(res);

                    })
                }} />
            }

            <Dialog_ />

        </View>
    );
});
