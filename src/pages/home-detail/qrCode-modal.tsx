import api from "@/util/api"
import { Icon, Popup } from "@antmjs/vantui"
import { View } from "@tarojs/components"
import { useEffect, useState } from "react"
import { Barcode, QRCode } from 'taro-code'
export default ({ data, go, renderLeft, load }) => {
    const [show, setShow] = useState(false);
    let timer;
    let count = 50
    useEffect(() => {
        if (show) {
            clearInterval(timer)
            timer = setInterval(() => {
                if (count <= 0) {
                    clearInterval(timer)
                }
                api.getMealStatus({
                    data: {
                        orderId: data.orderId
                    },
                    method: 'POST',
                    // showLoad: true,
                }).then((res) => {
                    console.log(res);
                    // setDetail(res);
                    if (res.data) {
                        setShow(false)
                        load && load()
                        clearInterval(timer)
                    }
                })
                count--;
            }, 3000);
        } else {
            load && load()
            clearInterval(timer)
        }
    }, [show])
    return (
        <>
            {
                renderLeft ?
                    renderLeft({
                        openFn: () => {
                            setShow(true)
                        }
                    })
                    :
                    <View className="order-code-btn" onClick={() => {
                        console.log('click')
                        if (data.multiUnTakeMeal) {
                            //去往订单中心
                            go && go();
                            return
                        }
                        setShow(true)
                    }}>
                        <Icon name='orders-o' size="32px" color="#fff" />
                    </View>
            }

            <Popup
                show={show}
                onClose={() => {
                    clearInterval(timer)
                        ; setShow(false)
                }} round>
                <View style={{
                    width: '300px', height: '400px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '64px'
                }}>
                    <View style={{ fontSize: '16px', marginBottom: '20px' }}>
                        取餐号:{data?.mealCode}
                    </View>
                    <View>
                        <QRCode
                            text={`<${data?.mealQrCode}>`}
                            size={200}
                            scale={4}
                            errorCorrectLevel='M'
                            typeNumber={2}
                        />
                    </View>


                </View>
            </Popup >
        </>
    )
}