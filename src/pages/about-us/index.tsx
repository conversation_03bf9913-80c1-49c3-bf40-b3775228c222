/**
 * @description 关于我们
 * <AUTHOR>
 */
import { RichText, View } from "@tarojs/components";
import "./index.less";
import { Swiper, SwiperItem, Image } from "@antmjs/vantui";
import api from "@/util/api";
import { useState, useEffect } from "react";
import { useShareAppMessage } from "@tarojs/taro";
definePageConfig({
    enableShareAppMessage: true
})
const AboutUs = () => {
    const [list, setList] = useState([]);
    const [descList, setDescList] = useState([]);
    // const images = [
    //     "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/about-us-2.jpg",
    //     "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/about-us-1.jpg",
    //     // "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/about-us-2.jpg",
    //     // "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/about-us-1.jpg",
    // ];

    const getAboutUsList = () => {
        api.getAboutUs({
            method: "get",
        }).then(res => {
            setList(res);
        });
    };

    const getAboutUsDescList = () => {
        api.getAboutUsDesc({
            method: "get",
        }).then(res => {
            setDescList(res);
        });
    };

    useEffect(() => {
        getAboutUsList();
        getAboutUsDescList();
    }, []);
    useShareAppMessage(() => {
        return {
            title: '7 RIVERLIGHT',
            path: '/pages/about-us/index'
        }
    })
    return (
        <View className="about-us">
            <View className="about-us-name">7 RIVERLIGHT</View>
            <View className="about-us-address">Hangzhou</View>
            <View className="about-us-main">
                {/* <View>
                    Founded in Hangzhou in2022, 7 Riverlight is dedicated to pastry and bagel making. Inspired by the
                    pastry and pantry shops of London and NYC, we are committed to creating pastry and environment with
                    fresh ingredients, warmth and love.
                </View>
                <View>
                    7
                    RIVERLIGHT创立于2022年，杭州。我们专注于制作糕点与贝果并受到来自伦敦和纽约的街头店铺的影响与启发。我们专注于创造最新鲜的甜点和充满温暖与爱的环境。
                </View> */}
                {descList.map((item: any, index: number) => {
                    return <RichText nodes={item?.desc} key={index}></RichText>;
                })}
                <Swiper
                    height={700}
                    paginationColor="#426543"
                    autoPlay="3000"
                    initPage={0}
                    // width={690}
                    style={{ width: "100%", marginTop: "0" }}
                    paginationVisible>
                    {list.map((item: any, index) => (
                        <SwiperItem key={`swiper#demo1${index}`}>
                            <Image src={item?.url} fit="widthFix" width="100%" height={`${700}px`} />
                        </SwiperItem>
                    ))}
                </Swiper>
                <View className="about-us-main-bottom-image"></View>
            </View>
        </View>
    );
};

export default AboutUs;
