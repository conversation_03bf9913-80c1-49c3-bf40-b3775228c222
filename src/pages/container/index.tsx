import "@/styles/variables.less";
import { Icon } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import Taro, { useDidShow, useRouter, useShareAppMessage } from "@tarojs/taro";
import { useEffect, useState } from "react";
import CartCHKIcon from "../../images/bag_chk.png";
import HomeCHKIcon from "../../images/home_chk.png";
import MyCHKIcon from "../../images/mine_chk.png";
import Home from "../home-detail/index";
import My from "../my/index";
import Order from "../order/order/index";
import "./index.less";
import React from "react";

const TABS = {
    0: Home,
    1: Order,
    // 2: Cart,
    2: My,
};

definePageConfig({
    enableShareAppMessage: true
})
export default () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const route = useRouter()
    const tabBars = [
        {
            title: "首页",
            icon: "wap-home-o",
            selectedIcon: HomeCHKIcon,
            ref: React.useRef()
        },
        // {
        //     title: '分类', icon: IndexIcon, selectedIcon: IndexCHKIcon,
        // },
        {
            title: "订单",
            icon: "orders-o",
            selectedIcon: CartCHKIcon,
            ref: React.useRef()
        },
        {
            title: "我的",
            icon: "user-o",
            selectedIcon: MyCHKIcon,
            ref: React.useRef()
        },
    ];
    useShareAppMessage(() => {
        return {
            title: '7 RIVERLIGHT',
            path: '/pages/container/index'
        }
    })
    useEffect(() => {
        Taro.setNavigationBarTitle({ title: "首页" });
        if (route.params.select) {
            setActiveIndex(activeIndex || 1)
        }
    }, []);
    useDidShow(() => {
        if (tabBars[activeIndex].ref.current) {
            //@ts-ignore
            tabBars[activeIndex].ref.current.load();
        }
    })
    const Component = TABS[activeIndex];
    return (
        <View className="app">
            {<Component ref={tabBars[activeIndex].ref} onTap={() => {
                setActiveIndex(1)
            }} goOrder={() => {
                setActiveIndex(1)
            }} />}
            <View className="container-footer">
                {tabBars.map((item, index) => {
                    return (
                        // eslint-disable-next-line react/jsx-key
                        <View
                            style={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                            onClick={() => {
                                setActiveIndex(index);
                                Taro.setNavigationBarTitle({
                                    title: item.title,

                                });
                                Taro.setNavigationBarColor({
                                    frontColor: '#000000',
                                    backgroundColor: index == 2 ? '#f1f2f3' : '#ffffff',
                                    animation: {
                                        duration: 400,
                                        timingFunc: 'easeIn'
                                    }
                                })
                            }}>
                            <Icon
                                name={item.icon}
                                style={{
                                    fontSize: "24px",
                                    color: index === activeIndex ? "#4b5c10" : "#d4cece",
                                    marginBottom: "2px"
                                }}
                            />
                            <View
                                style={{
                                    color: index === activeIndex ? "#4b5c10" : "#d4cece",
                                }}>
                                {item.title}
                            </View>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};
