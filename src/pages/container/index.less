@import "@/styles/variables.less";
.app{
    min-height: 100vh;
    background-color: #f1f2f3;
}
.container-footer {
    position: fixed;
    bottom: calc(30px + env(safe-area-inset-bottom));
    width: calc(100% - 48px);
    left: 24px;
    right: 24px;
    padding: 16px 0;
    border-radius: 60px;
    background-color: #fff;
    z-index: 999;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    // box-shadow: 0 0 10px #919191; //h-shadow v-shadow blur color
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    transition: box-shadow 0.3s;
    .icon {
        font-size: 24px;
    }
}
