.select-shop {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .header-container {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    padding: 20px;
    color: white;

    .header-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 4px;
      display: block;
    }

    .header-subtitle {
      font-size: 14px;
      opacity: 0.9;
      display: block;
    }
  }

  .search-container {
    padding: 16px;
    background: white;
    border-bottom: 1px solid #eee;

    .search-box {
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-radius: 8px;
      padding: 8px 12px;
      gap: 8px;

      .search-input {
        flex: 1;
        font-size: 14px;
        color: #333;

        &::placeholder {
          color: #999;
        }
      }
    }
  }

  .shop-list {
    flex: 1;
    background: white;

    .loading {
      padding: 40px;
      text-align: center;
      color: #999;
    }

    .shop-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background: white;
      transition: background-color 0.2s;

      &:active {
        background-color: #f8f8f8;
      }

      &:last-child {
        border-bottom: none;
      }

      .shop-info {
        flex: 1;
        margin-right: 16px;

        .shop-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .shop-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
          }

          .distance {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #666;

            .distance-text {
              color: #ff6b35;
              font-weight: 500;
            }
          }
        }

        .shop-address,
        .shop-hours {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 4px;

          .address-text,
          .hours-text {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
          }
        }

        .shop-address {
          .address-text {
            flex: 1;
          }
        }
      }

      .shop-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .action-btn {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #fff;
          border: 1px solid #ff6b35;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;

          &:active {
            background: #ff6b35;
            
            .van-icon {
              color: white !important;
            }
          }

          &.phone-btn {
            border-color: #ff6b35;
          }

          &.map-btn {
            border-color: #ff6b35;
          }
        }
      }
    }
  }
}

/* 适配不同屏幕尺寸 */
@media (max-height: 600px) {
  .select-shop {
    .map-container {
      height: 300px;
    }
  }
}

@media (min-height: 800px) {
  .select-shop {
    .map-container {
      height: 500px;
    }
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .select-shop {
    background-color: #1a1a1a;

    .search-container {
      background: #2a2a2a;
      border-bottom-color: #333;

      .search-box {
        background: #333;

        .search-input {
          color: #fff;

          &::placeholder {
            color: #666;
          }
        }
      }
    }

    .shop-list {
      background: #2a2a2a;

      .shop-item {
        background: #2a2a2a;
        border-bottom-color: #333;

        &:active {
          background-color: #333;
        }

        .shop-info {
          .shop-header {
            .shop-name {
              color: #fff;
            }
          }

          .shop-address,
          .shop-hours {
            .address-text,
            .hours-text {
              color: #ccc;
            }
          }
        }

        .shop-actions {
          .action-btn {
            background: #333;
            border-color: #ff6b35;
          }
        }
      }
    }
  }
}
