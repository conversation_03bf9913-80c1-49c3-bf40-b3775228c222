.select-shop {
  height: 100vh;
  position: relative;
  background-color: #f5f5f5;

  .map-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50vh;
    z-index: 1;

    #shop-map {
      width: 100%;
      height: 100%;
    }

    .map-controls {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10;

      .map-toggle-btn {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 8px 16px;
        display: flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
          background: rgba(255, 255, 255, 0.8);
        }

        .toggle-text {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }

  .content-area {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: transparent;

    &.with-map {
      margin-top: 50vh;
      height: 50vh;
      background: white;
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    }

    &.full-screen {
      height: 100vh;
      margin-top: 0;
      background: #f5f5f5;
    }

    .header-container {
      background: linear-gradient(135deg, #ff6b35, #f7931e);
      padding: 20px;
      color: white;
      position: relative;

      .header-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;
        display: block;
      }

      .header-subtitle {
        font-size: 14px;
        opacity: 0.9;
        display: block;
      }

      .show-map-btn {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        gap: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 6px 12px;
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(0.95);
        }

        .show-map-text {
          font-size: 12px;
          color: white;
        }
      }
    }

    &.with-map .header-container {
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
    }
  }

  .search-container {
    padding: 16px;
    background: white;
    border-bottom: 1px solid #eee;
    flex-shrink: 0;

    .search-box {
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-radius: 8px;
      padding: 8px 12px;
      gap: 8px;

      .search-input {
        flex: 1;
        font-size: 14px;
        color: #333;

        &::placeholder {
          color: #999;
        }
      }
    }
  }

  .shop-list {
    flex: 1;
    background: white;
    overflow: hidden;

    .loading {
      padding: 40px;
      text-align: center;
      color: #999;
    }

    .shop-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background: white;
      transition: background-color 0.2s;

      &:active {
        background-color: #f8f8f8;
      }

      &:last-child {
        border-bottom: none;
      }

      .shop-info {
        flex: 1;
        margin-right: 16px;

        .shop-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .shop-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
          }

          .distance {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #666;

            .distance-text {
              color: #ff6b35;
              font-weight: 500;
            }
          }
        }

        .shop-address,
        .shop-hours {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 4px;

          .address-text,
          .hours-text {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
          }
        }

        .shop-address {
          .address-text {
            flex: 1;
          }
        }
      }

      .shop-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .action-btn {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #fff;
          border: 1px solid #ff6b35;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;

          &:active {
            background: #ff6b35;
            
            .van-icon {
              color: white !important;
            }
          }

          &.phone-btn {
            border-color: #ff6b35;
          }

          &.map-btn {
            border-color: #ff6b35;
          }
        }
      }
    }
  }
}

/* 适配不同屏幕尺寸 */
@media (max-height: 600px) {
  .select-shop {
    .map-background {
      height: 40vh;
    }

    .content-area.with-map {
      margin-top: 40vh;
      height: 60vh;
    }
  }
}

@media (min-height: 800px) {
  .select-shop {
    .map-background {
      height: 55vh;
    }

    .content-area.with-map {
      margin-top: 55vh;
      height: 45vh;
    }
  }
}

/* 动画效果 */
.content-area {
  transition: all 0.3s ease-in-out;
}

.map-background {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .select-shop {
    background-color: #1a1a1a;

    .search-container {
      background: #2a2a2a;
      border-bottom-color: #333;

      .search-box {
        background: #333;

        .search-input {
          color: #fff;

          &::placeholder {
            color: #666;
          }
        }
      }
    }

    .shop-list {
      background: #2a2a2a;

      .shop-item {
        background: #2a2a2a;
        border-bottom-color: #333;

        &:active {
          background-color: #333;
        }

        .shop-info {
          .shop-header {
            .shop-name {
              color: #fff;
            }
          }

          .shop-address,
          .shop-hours {
            .address-text,
            .hours-text {
              color: #ccc;
            }
          }
        }

        .shop-actions {
          .action-btn {
            background: #333;
            border-color: #ff6b35;
          }
        }
      }
    }
  }
}
