.select-shop {
  height: 100vh;
  position: relative;
  background-color: #f5f5f5;

  .map-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60vh;
    z-index: 1;

    #shop-map {
      width: 100%;
      height: 100%;
    }

    .map-controls {
      position: absolute;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10;

      .map-toggle-btn {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 12px 20px;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        min-width: 120px;
        justify-content: center;

        &:active {
          transform: scale(0.95);
          background: rgba(255, 255, 255, 0.8);
        }

        .toggle-text {
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }

  .content-area {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: transparent;

    &.with-map {
      margin-top: 60vh;
      height: 40vh;
      background: white;
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
      box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    }

    &.full-screen {
      height: 100vh;
      margin-top: 0;
      background: #f5f5f5;
    }

    .header-container {
      background: linear-gradient(135deg, #ff6b35, #f7931e);
      padding: 24px 20px;
      color: white;
      position: relative;
      flex-shrink: 0;

      .header-title {
        font-size: 22px;
        font-weight: 600;
        margin-bottom: 6px;
        display: block;
      }

      .header-subtitle {
        font-size: 15px;
        opacity: 0.9;
        display: block;
      }

      .show-map-btn {
        position: absolute;
        top: 24px;
        right: 20px;
        display: flex;
        align-items: center;
        gap: 6px;
        background: rgba(255, 255, 255, 0.25);
        border-radius: 20px;
        padding: 8px 16px;
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 255, 255, 0.35);
          transform: scale(0.95);
        }

        .show-map-text {
          font-size: 13px;
          color: white;
          font-weight: 500;
        }
      }
    }

    &.with-map .header-container {
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
    }
  }

  .search-container {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #eee;
    flex-shrink: 0;

    .search-box {
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-radius: 12px;
      padding: 12px 16px;
      gap: 10px;
      min-height: 44px;

      .search-input {
        flex: 1;
        font-size: 16px;
        color: #333;

        &::placeholder {
          color: #999;
        }
      }
    }
  }

  .shop-list {
    flex: 1;
    background: white;
    overflow: hidden;

    .loading {
      padding: 60px 40px;
      text-align: center;
      color: #999;
      font-size: 16px;
    }

    .shop-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      background: white;
      transition: background-color 0.2s;
      min-height: 100px;

      &:active {
        background-color: #f8f8f8;
      }

      &:last-child {
        border-bottom: none;
      }

      .shop-info {
        flex: 1;
        margin-right: 20px;

        .shop-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          .shop-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
          }

          .distance {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;

            .distance-text {
              color: #ff6b35;
              font-weight: 600;
              font-size: 14px;
            }
          }
        }

        .shop-address,
        .shop-hours {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 6px;

          .address-text,
          .hours-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
          }
        }

        .shop-address {
          .address-text {
            flex: 1;
          }
        }
      }

      .shop-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .action-btn {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: #fff;
          border: 2px solid #ff6b35;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;
          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);

          &:active {
            background: #ff6b35;
            transform: scale(0.95);

            .van-icon {
              color: white !important;
            }
          }

          &.phone-btn {
            border-color: #ff6b35;
          }

          &.map-btn {
            border-color: #ff6b35;
          }
        }
      }
    }
  }
}

/* 适配不同屏幕尺寸 */
@media (max-height: 600px) {
  .select-shop {
    .map-background {
      height: 50vh;
    }

    .content-area.with-map {
      margin-top: 50vh;
      height: 50vh;
    }
  }
}

@media (min-height: 800px) {
  .select-shop {
    .map-background {
      height: 65vh;
    }

    .content-area.with-map {
      margin-top: 65vh;
      height: 35vh;
    }
  }
}

@media (min-height: 900px) {
  .select-shop {
    .map-background {
      height: 70vh;
    }

    .content-area.with-map {
      margin-top: 70vh;
      height: 30vh;
    }
  }
}

/* 动画效果 */
.content-area {
  transition: all 0.3s ease-in-out;
}

.map-background {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .select-shop {
    background-color: #1a1a1a;

    .search-container {
      background: #2a2a2a;
      border-bottom-color: #333;

      .search-box {
        background: #333;

        .search-input {
          color: #fff;

          &::placeholder {
            color: #666;
          }
        }
      }
    }

    .shop-list {
      background: #2a2a2a;

      .shop-item {
        background: #2a2a2a;
        border-bottom-color: #333;

        &:active {
          background-color: #333;
        }

        .shop-info {
          .shop-header {
            .shop-name {
              color: #fff;
            }
          }

          .shop-address,
          .shop-hours {
            .address-text,
            .hours-text {
              color: #ccc;
            }
          }
        }

        .shop-actions {
          .action-btn {
            background: #333;
            border-color: #ff6b35;
          }
        }
      }
    }
  }
}
