/**
 * @description 选择店铺
 * <AUTHOR> Assistant
 */
// @ts-nocheck
import { MiniNavigateTo } from "@/util/route";
import { View, Text, Input, ScrollView } from "@tarojs/components";
import { Icon } from '@antmjs/vantui'
import React, { useEffect, useState } from "react";
import "./index.less";
import { observer } from "mobx-react";
import shopStore from "@/store/shopInfo";
import Taro from "@tarojs/taro";

definePageConfig({
    navigationBarTitleText: '选择门店'
});

interface ShopItem {
    id: number;
    shopName: string;
    address: string;
    latitude: number;
    longitude: number;
    distance?: number;
    businessHours: string;
    phone?: string;
}

const SelectShop = observer(() => {
    const [searchText, setSearchText] = useState('');
    const [shopList, setShopList] = useState<ShopItem[]>([]);
    const [loading, setLoading] = useState(false);

    // 加载店铺列表
    const loadShopList = async () => {
        setLoading(true);
        try {
            // 模拟数据，实际应该从API获取
            const mockShops: ShopItem[] = [
                {
                    id: 1,
                    shopName: "食品加盟门店",
                    address: "岳麓区岳山路麓谷小镇北侧430米",
                    latitude: 28.2358,
                    longitude: 112.9358,
                    businessHours: "24小时营业",
                    phone: "0731-88888888",
                    distance: 0.1
                },
                {
                    id: 2,
                    shopName: "常德洋云店",
                    address: "武陵大道北段洋云广场",
                    latitude: 29.0458,
                    longitude: 111.6958,
                    businessHours: "24小时营业",
                    phone: "0736-88888888",
                    distance: 148.7
                },
                {
                    id: 3,
                    shopName: "小花测试门店",
                    address: "宝安区宝民一路108号",
                    latitude: 22.5558,
                    longitude: 113.8858,
                    businessHours: "24小时营业",
                    phone: "0755-88888888",
                    distance: 637.2
                }
            ];

            setShopList(mockShops);
        } catch (error) {
            console.error('加载店铺列表失败:', error);
            Taro.showToast({
                title: '加载失败',
                icon: 'none'
            });
        } finally {
            setLoading(false);
        }
    };

    // 选择店铺
    const selectShop = (shop: ShopItem) => {
        shopStore.setSelectedShop(shop);
        Taro.showToast({
            title: '已选择店铺',
            icon: 'success'
        });

        setTimeout(() => {
            Taro.navigateBack();
        }, 1000);
    };

    // 打开地图导航
    const openMap = (shop: ShopItem) => {
        Taro.openLocation({
            latitude: shop.latitude,
            longitude: shop.longitude,
            name: shop.shopName,
            address: shop.address,
            scale: 18
        });
    };

    // 拨打电话
    const makePhoneCall = (phone: string) => {
        if (phone) {
            Taro.makePhoneCall({
                phoneNumber: phone
            });
        }
    };

    // 过滤店铺列表
    const filteredShops = shopList.filter(shop =>
        shop.shopName.toLowerCase().includes(searchText.toLowerCase()) ||
        shop.address.toLowerCase().includes(searchText.toLowerCase())
    );

    useEffect(() => {
        loadShopList();
    }, []);

    return (
        <View className="select-shop">
            {/* 顶部标题区域 */}
            <View className="header-container">
                <Text className="header-title">请选择门店</Text>
                <Text className="header-subtitle">选择最近的门店享受服务</Text>
            </View>

            {/* 搜索框 */}
            <View className="search-container">
                <View className="search-box">
                    <Icon name="search" size="16" color="#999" />
                    <Input
                        className="search-input"
                        placeholder="搜索"
                        value={searchText}
                        onInput={(e) => setSearchText(e.detail.value)}
                    />
                </View>
            </View>

            {/* 店铺列表 */}
            <ScrollView className="shop-list" scrollY>
                {loading ? (
                    <View className="loading">
                        <Text>加载中...</Text>
                    </View>
                ) : (
                    filteredShops.map(shop => (
                        <View key={shop.id} className="shop-item" onClick={() => selectShop(shop)}>
                            <View className="shop-info">
                                <View className="shop-header">
                                    <Text className="shop-name">{shop.shopName}</Text>
                                    <View className="distance">
                                        <Text>距离</Text>
                                        <Text className="distance-text">
                                            {shop.distance ?
                                                shop.distance < 1 ?
                                                    `小于100m` :
                                                    `${shop.distance}km`
                                                : '小于100m'
                                            }
                                        </Text>
                                    </View>
                                </View>

                                <View className="shop-address">
                                    <Icon name="location-o" size="14" color="#999" />
                                    <Text className="address-text">{shop.address}</Text>
                                </View>

                                <View className="shop-hours">
                                    <Icon name="clock-o" size="14" color="#999" />
                                    <Text className="hours-text">{shop.businessHours}</Text>
                                </View>
                            </View>

                            <View className="shop-actions">
                                {shop.phone && (
                                    <View
                                        className="action-btn phone-btn"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            makePhoneCall(shop.phone!);
                                        }}
                                    >
                                        <Icon name="phone-o" size="20" color="#ff6b35" />
                                    </View>
                                )}

                                <View
                                    className="action-btn map-btn"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        openMap(shop);
                                    }}
                                >
                                    <Icon name="guide-o" size="20" color="#ff6b35" />
                                </View>
                            </View>
                        </View>
                    ))
                )}
            </ScrollView>
        </View>
    );
});

export default SelectShop;
