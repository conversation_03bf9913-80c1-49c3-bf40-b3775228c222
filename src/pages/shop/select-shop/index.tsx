/**
 * @description 选择店铺
 * <AUTHOR> Assistant
 */
// @ts-nocheck
import { MiniNavigateTo } from "@/util/route";
import { View, Text, Input, ScrollView, Map } from "@tarojs/components";
import { Icon } from '@antmjs/vantui'
import React, { useEffect, useState } from "react";
import "./index.less";
import { observer } from "mobx-react";
import shopStore from "@/store/shopInfo";
import Taro from "@tarojs/taro";

definePageConfig({
    navigationBarTitleText: '选择门店'
});

interface ShopItem {
    id: number;
    shopName: string;
    address: string;
    latitude: number;
    longitude: number;
    distance?: number;
    businessHours: string;
    phone?: string;
}

const SelectShop = observer(() => {
    const [searchText, setSearchText] = useState('');
    const [shopList, setShopList] = useState<ShopItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [userLocation, setUserLocation] = useState<{ latitude: number, longitude: number } | null>(null);
    const [showMap, setShowMap] = useState(true);

    // 获取用户当前位置
    const getUserLocation = () => {
        Taro.getLocation({
            type: 'gcj02',
            success: (res) => {
                setUserLocation({
                    latitude: res.latitude,
                    longitude: res.longitude
                });
                calculateDistances(shopList, res.latitude, res.longitude);
            },
            fail: (err) => {
                console.log('获取位置失败:', err);
                // 设置默认位置（长沙市中心）
                setUserLocation({
                    latitude: 28.2358,
                    longitude: 112.9358
                });
            }
        });
    };

    // 计算距离
    const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
        const radLat1 = lat1 * Math.PI / 180.0;
        const radLat2 = lat2 * Math.PI / 180.0;
        const a = radLat1 - radLat2;
        const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
        let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
            Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * 6378.137;
        s = Math.round(s * 10000) / 10000;
        return s;
    };

    // 计算所有店铺距离
    const calculateDistances = (shops: ShopItem[], userLat: number, userLng: number) => {
        const shopsWithDistance = shops.map(shop => ({
            ...shop,
            distance: calculateDistance(userLat, userLng, shop.latitude, shop.longitude)
        }));

        // 按距离排序
        shopsWithDistance.sort((a, b) => (a.distance || 0) - (b.distance || 0));
        setShopList(shopsWithDistance);
    };

    // 加载店铺列表
    const loadShopList = async () => {
        setLoading(true);
        try {
            // 模拟数据，实际应该从API获取
            const mockShops: ShopItem[] = [
                {
                    id: 1,
                    shopName: "食品加盟门店",
                    address: "岳麓区岳山路麓谷小镇北侧430米",
                    latitude: 28.2358,
                    longitude: 112.9358,
                    businessHours: "24小时营业",
                    phone: "0731-88888888",
                    distance: 0.1
                },
                {
                    id: 2,
                    shopName: "常德洋云店",
                    address: "武陵大道北段洋云广场",
                    latitude: 29.0458,
                    longitude: 111.6958,
                    businessHours: "24小时营业",
                    phone: "0736-88888888",
                    distance: 148.7
                },
                {
                    id: 3,
                    shopName: "小花测试门店",
                    address: "宝安区宝民一路108号",
                    latitude: 22.5558,
                    longitude: 113.8858,
                    businessHours: "24小时营业",
                    phone: "0755-88888888",
                    distance: 637.2
                }
            ];

            setShopList(mockShops);

            // 如果已有用户位置，重新计算距离
            if (userLocation) {
                calculateDistances(mockShops, userLocation.latitude, userLocation.longitude);
            }
        } catch (error) {
            console.error('加载店铺列表失败:', error);
            Taro.showToast({
                title: '加载失败',
                icon: 'none'
            });
        } finally {
            setLoading(false);
        }
    };

    // 选择店铺
    const selectShop = (shop: ShopItem) => {
        shopStore.setSelectedShop(shop);
        Taro.showToast({
            title: '已选择店铺',
            icon: 'success'
        });

        setTimeout(() => {
            Taro.navigateBack();
        }, 1000);
    };

    // 打开地图导航
    const openMap = (shop: ShopItem) => {
        Taro.openLocation({
            latitude: shop.latitude,
            longitude: shop.longitude,
            name: shop.shopName,
            address: shop.address,
            scale: 18
        });
    };

    // 拨打电话
    const makePhoneCall = (phone: string) => {
        if (phone) {
            Taro.makePhoneCall({
                phoneNumber: phone
            });
        }
    };

    // 过滤店铺列表
    const filteredShops = shopList.filter(shop =>
        shop.shopName.toLowerCase().includes(searchText.toLowerCase()) ||
        shop.address.toLowerCase().includes(searchText.toLowerCase())
    );

    useEffect(() => {
        getUserLocation();
        loadShopList();
    }, []);

    return (
        <View className="select-shop">
            {/* 地图背景 */}
            {showMap && userLocation && (
                <View className="map-background">
                    <Map
                        id="shop-map"
                        longitude={userLocation.longitude}
                        latitude={userLocation.latitude}
                        scale={12}
                        showLocation
                        markers={shopList.map(shop => ({
                            id: shop.id,
                            latitude: shop.latitude,
                            longitude: shop.longitude,
                            title: shop.shopName,
                            iconPath: '',
                            width: 30,
                            height: 30,
                            callout: {
                                content: shop.shopName,
                                color: '#000',
                                fontSize: 24,
                                borderRadius: 4,
                                bgColor: '#fff',
                                padding: 8,
                                display: 'ALWAYS'
                            }
                        }))}
                        onMarkerTap={(e) => {
                            const shop = shopList.find(s => s.id === e.detail.markerId);
                            if (shop) {
                                selectShop(shop);
                            }
                        }}
                    />

                    {/* 地图控制按钮 */}
                    <View className="map-controls">
                        <View
                            className="map-toggle-btn"
                            onClick={() => setShowMap(false)}
                        >
                            <Icon name="arrow-up" size="16" color="#666" />
                            <Text className="toggle-text">收起地图</Text>
                        </View>
                    </View>
                </View>
            )}

            {/* 内容区域 */}
            <View className={`content-area ${showMap ? 'with-map' : 'full-screen'}`}>
                {/* 顶部标题区域 */}
                <View className="header-container">
                    <Text className="header-title">请选择门店</Text>
                    <Text className="header-subtitle">选择最近的门店享受服务</Text>
                    {!showMap && (
                        <View
                            className="show-map-btn"
                            onClick={() => setShowMap(true)}
                        >
                            <Icon name="location-o" size="16" color="#ff6b35" />
                            <Text className="show-map-text">显示地图</Text>
                        </View>
                    )}
                </View>

                {/* 搜索框 */}
                <View className="search-container">
                    <View className="search-box">
                        <Icon name="search" size="16" color="#999" />
                        <Input
                            className="search-input"
                            placeholder="搜索"
                            value={searchText}
                            onInput={(e) => setSearchText(e.detail.value)}
                        />
                    </View>
                </View>

                {/* 店铺列表 */}
                <ScrollView className="shop-list" scrollY>
                    {loading ? (
                        <View className="loading">
                            <Text>加载中...</Text>
                        </View>
                    ) : (
                        filteredShops.map(shop => (
                            <View key={shop.id} className="shop-item" onClick={() => selectShop(shop)}>
                                <View className="shop-info">
                                    <View className="shop-header">
                                        <Text className="shop-name">{shop.shopName}</Text>
                                        <View className="distance">
                                            <Text>距离</Text>
                                            <Text className="distance-text">
                                                {shop.distance ?
                                                    shop.distance < 1 ?
                                                        `小于100m` :
                                                        `${shop.distance}km`
                                                    : '小于100m'
                                                }
                                            </Text>
                                        </View>
                                    </View>

                                    <View className="shop-address">
                                        <Icon name="location-o" size="14" color="#999" />
                                        <Text className="address-text">{shop.address}</Text>
                                    </View>

                                    <View className="shop-hours">
                                        <Icon name="clock-o" size="14" color="#999" />
                                        <Text className="hours-text">{shop.businessHours}</Text>
                                    </View>
                                </View>

                                <View className="shop-actions">
                                    {shop.phone && (
                                        <View
                                            className="action-btn phone-btn"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                makePhoneCall(shop.phone!);
                                            }}
                                        >
                                            <Icon name="phone-o" size="20" color="#ff6b35" />
                                        </View>
                                    )}

                                    <View
                                        className="action-btn map-btn"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            openMap(shop);
                                        }}
                                    >
                                        <Icon name="guide-o" size="20" color="#ff6b35" />
                                    </View>
                                </View>
                            </View>
                        ))
                    )}
                </ScrollView>
            </View>
        </View>
    );
});

export default SelectShop;
