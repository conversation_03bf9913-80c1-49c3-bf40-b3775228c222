import { Image, View, Text, Input, But<PERSON>, PageContainer } from "@tarojs/components"
import './index.less'
import { useEffect, useState } from "react"
import api from "@/util/api"
import Taro, { useUnload, useDidShow, useRouter } from "@tarojs/taro"
import { MiniNavigateTo, MiniRedirectTo } from "@/util/route"
import MiniToast from "@/util/toast"
import { MiniObject } from "src/type/common"
import { observer, useObserver } from "mobx-react-lite";
import confirmOrderStore from "@/store/confirmOrder"
import RemarkModal from "./remark-modal"
import { Icon } from "@antmjs/vantui"
import LimitTimePicker from "@/components/limit-time-picker"
import shopStore from "@/store/shopInfo"
definePageConfig({
    navigationBarTitleText: '订单结算',
    navigationBarTextStyle: 'white',
    // backgroundColor: '#a1a789'
    navigationBarBackgroundColor: '#a1a789'
})
export default observer(() => {
    const route = useRouter();
    const store = confirmOrderStore.getAddress();
    // const coupon = confirmOrderStore.getCoupon();
    const [methodSelect, setMethodSelect] = useState(confirmOrderStore.freightType);
    const [yyTime, setYyTime] = useState(confirmOrderStore.getTime());
    const [address, setAddress] = useState<MiniObject>(store)
    const [coupon, setCoupon] = useState<MiniObject>(confirmOrderStore.getCoupon())
    const [remarks, setRemarks] = useState('')
    const [shops, setShops] = useState<any[]>([])
    const [orderTotal, setOrderTotal] = useState(0.00)
    const [remarkShow, setRemarkShow] = useState(false)
    const [goodsTotalPrice, setGoodsTotalPrice] = useState(0)
    const [goodsTotalNum, setgoodsTotalNum] = useState(0)
    const [freightPrice, setFreightPrice] = useState(0)
    const [packingFee, setPackingFee] = useState(0);
    const [telephone, setTelephone] = useState('')
    const [forward, setForward] = useState('')
    const [other, setOther] = useState({
        allowSubmitOrder: true,
        reason: '',
        couponUserId: null
    })
    const submitOrder = () => {
        if (methodSelect == "0" && !other?.allowSubmitOrder) {
            return MiniToast.info(other.reason)
        }
        //@ts-ignore
        // const carts = JSON.parse(route.params.carts);
        const cartId = {};
        // console.log("shops:", shops)
        shops.map((item) => {
            cartId[item.id] = null;
        })
        if (methodSelect == "0" && !address.id) {
            return MiniToast.info("请选择配送地址")
        }
        if (!telephone) {
            return MiniToast.info("请填写联系方式，方便商家联系")
        }
        if (!/1[3-9]\d{9}/.test(telephone)) {
            return MiniToast.info("请填写正确11位手机号码")
        }
        // console.log("cartId:", cartId)
        api.submitOrder({
            data: +methodSelect === 0 ? {
                shopId: 1,
                cartIdRemarkMap: cartId,
                addressId: address?.id,
                "couponUserId": other?.couponUserId || 0,
                freightType: +methodSelect,
                remark: remarks,
                freightPrice: freightPrice,
                packingFee: packingFee || 0,
                telephone: telephone,
                couponCode: forward
            } : {
                shopId: 1,
                cartIdRemarkMap: cartId,
                "couponUserId": other?.couponUserId || 0,
                freightType: +methodSelect,
                remark: "取餐时间:" + (yyTime || '立即取单') + "；" + remarks,
                packingFee: packingFee || 0,
                telephone: telephone,
                couponCode: forward
            },
            method: 'POST',
        }).then(res => {
            MiniRedirectTo({
                url: '/pages/order/cashier/index?orderNo=' + res
            });
        }).catch((err) => {

        })
    }

    const getDetail = () => {
        try {
            //@ts-ignore
            const carts = JSON.parse(route.params.carts);
            const addressId = confirmOrderStore.getAddress()?.id;
            const freightType = confirmOrderStore.freightType;
            const couponUserId = confirmOrderStore.getCoupon().couponUserId
            api.preOrder({
                data: {
                    // userId: 2,
                    shopId: 1,
                    cartIdList: carts,
                    addressId: addressId,
                    freightType: +freightType,
                    couponUserId: couponUserId,
                },
                method: 'post',
            }).then(res => {
                console.log(res)
                setShops(res.checkedGoodsList || [])
                // 缓存数据
                confirmOrderStore.setCoupon({
                    id: res.couponId,
                    couponUserId: res.couponUserId,
                    couponPrice: res.couponPrice,
                    couponName: res.couponName
                })
                console.log("setcoupon")
                setCoupon({
                    id: res.couponId,
                    couponUserId: res.couponUserId,
                    couponPrice: res.couponPrice,
                    couponName: res.couponName
                })
                setOrderTotal(res.actualPrice)
                setGoodsTotalPrice(res.goodsTotalPrice)
                setFreightPrice(res.freightPrice)
                setPackingFee(res.packingFee)
                let totalCount = 0
                res.checkedGoodsList.map((item) => {
                    totalCount += item.number
                })
                setgoodsTotalNum(totalCount)
                setOther({
                    allowSubmitOrder: res.allowSubmitOrder,
                    reason: res.reason,
                    couponUserId: res.couponUserId,
                    // telephone:res.telephone
                })
                setTelephone(res.telephone)
            }).catch((err) => {

            })
        } catch (error) {

        }

    }


    useDidShow(() => {
        setAddress(confirmOrderStore.getAddress());
        // setCoupon(confirmOrderStore.getCoupon());
        getDetail();
    })

    useUnload(() => {
        console.log("页面关闭了")
        confirmOrderStore.setCoupon({})
    })

    console.log("coupon:", coupon)

    return (<View className="confirm-order">
        <View className="content">
            <View className="shop-rect">
                <View className="shop-rect-name">
                    <Icon name='location' size='40' color='#fff' />
                    {/* 7 RIVERLIGHT(钱江新城店) */}
                    {shopStore.shopInfo.shopName || '7 RIVERLIGHT'}
                </View>
                <View className="shop-rect-desc">
                    {
                        confirmOrderStore.initFreightType === 0 &&
                        <View style={{ display: 'flex', alignItems: 'center', flexDirection: 'row', padding: '10px 0' }}
                            onClick={() => {
                                // latitude: 30.25162525902864
                                // longitude: 120.20763915525751
                                Taro.openLocation({
                                    address: shopStore.shopInfo.address,
                                    latitude: Number(shopStore.shopInfo.latitude || 30.25162525902864),
                                    longitude: Number(shopStore.shopInfo.longitude || 120.20763915525751),

                                    success(result) {
                                        console.log("result:", result)
                                    },
                                    fail(res) {
                                        console.log("res:", res)
                                    },
                                })
                            }}
                        >
                            <Text style={{ fontSize: '14px', color: '#ccc' }}>门店地址：</Text>
                            <View style={{ fontSize: '16px' }}>
                                {shopStore.shopInfo.address}
                            </View>
                            {/* <Icon name='arrow' size={'32'} color="#a14e42" style={{ marginRight: '10px' }} /> */}
                        </View>
                    }
                </View>
            </View>
            <View className="order-method">
                {
                    confirmOrderStore.initFreightType === 1 && <View
                        onClick={() => {
                            setMethodSelect('1');
                            confirmOrderStore.freightType = '1';
                            setTimeout(() => {
                                getDetail();
                            }, 100)
                        }}
                        className={methodSelect == '1' ? 'selected method-item' : 'method-item'}>
                        <Icon name='bag-o' size={'40'} color="#666" style={{ marginRight: '10px' }} />
                        打包自取
                    </View>
                }
                {
                    confirmOrderStore.initFreightType === 1 && <View
                        onClick={() => {
                            if (confirmOrderStore.initFreightType === 0) {
                                return
                            }
                            setMethodSelect('2');
                            confirmOrderStore.freightType = '2';
                            setTimeout(() => {
                                getDetail();
                            }, 100)

                        }}
                        className={methodSelect == '2' ? 'selected method-item' : 'method-item'}>
                        <Icon name='shop-o' size={'40'} color="#666" style={{ marginRight: '10px' }} />

                        店内就餐
                    </View>
                }

                {/* {
                    confirmOrderStore.initFreightType === 0 && <View
                        onClick={() => {
                            setMethodSelect('0');
                            confirmOrderStore.freightType = '0';
                            setTimeout(() => {
                                getDetail();
                            }, 100)
                        }}
                        className={methodSelect == '0' ? 'selected method-item' : 'method-item'}>
                        <Icon name='bag-o' size={'40'} color="#666" style={{ marginRight: '10px' }} />
                        商家派送
                    </View>
                } */}
            </View>
            <View className="order-content">
                {
                    methodSelect == '0' &&
                    <View className="order-item">
                        <View className="order-item-title">
                            <Icon name='location-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />
                            配送地址</View>
                        <View className="order-item-content" onClick={() => {
                            MiniNavigateTo({
                                url: '/pages/order/address/index?type=select&from=order'
                            });
                        }}>
                            {address.id ? <View >
                                <View>{address.name} {address.mobile}</View>
                                <View className="address-detail">{address.addressLocation}</View>
                            </View> : '请选择配送信息'}
                        </View>
                    </View>
                }
                {
                    ['1', '2'].includes(methodSelect) &&
                    <View className="order-item">
                        <View className="order-item-title">
                            <Icon name='clock-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />
                            预约时间</View>
                        <LimitTimePicker value={yyTime} onChange={(value) => {
                            console.log("LimitTimePicker", value)
                            confirmOrderStore.setTime(value);
                            setYyTime(value)
                        }} />
                    </View>
                }
                <View className="goods">
                    {
                        shops.map((shop: any) => (
                            <View className="goods-item">
                                <Image src={shop.picUrl} mode='aspectFill' className="goods-item-img" />
                                <View className="goods-item-content">
                                    <View className="goods-item-left">
                                        <View className="goods-item-name">
                                            {shop.goodsName}
                                        </View>
                                        <View className="goods-item-spe">{shop.specifications}</View>
                                    </View>

                                    <View className="goods-item-right">
                                        {/* <View className="goods-item-price">¥{shop.price}</View> */}
                                        {
                                            shop.discountFlag ?
                                                <View >
                                                    <View className="goods-item-price" style={{ textDecoration: 'line-through' }}>￥{shop.price}</View>
                                                    <View className="goods-item-price" style={{ marginLeft: '8px', color: 'red' }}>¥{shop.discountPrice}</View>
                                                </View>
                                                :
                                                <View className="goods-item-price">￥{shop.price}</View>

                                        }
                                        <View className="goods-item-count">x{shop.number}</View>
                                    </View>
                                </View>
                            </View>
                        ))
                    }
                </View>
                <View className="order-item">
                    <View className="order-item-title">
                        <Icon name='shopping-cart-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                        商品总价</View>
                    <View className="order-item-content">
                        <View className="order-item-empty">¥{goodsTotalPrice || 0.00}</View>
                    </View>
                </View>
                <View className="order-item"
                    onClick={() => {
                        // if (!other.couponUserId) {
                        //     MiniToast.error('无可用优惠券');
                        //     return
                        // }
                        MiniNavigateTo({
                            url: `/pages/order/select-coupon/index?couponUserId=${other.couponUserId}`
                        })
                    }}
                >
                    <View className="order-item-title">
                        <Icon name='coupon-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                        优惠券</View>
                    <View className="order-item-content">
                        {
                            coupon?.couponUserId ?
                                <>
                                    <View>{coupon.couponName}</View>
                                    <View style={{ color: 'red', fontSize: '14px', textAlign: "right" }}>-¥{coupon.couponPrice}</View>
                                </>

                                :
                                <Text className="order-item-empty">暂无</Text>
                        }
                    </View>
                </View>
                {
                    methodSelect == '0' &&
                    <>
                        <View className="order-item">
                            <View className="order-item-title">
                                <Icon name='balance-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                                运费</View>
                            <View className="order-item-content">
                                <View className="order-item-empty">¥{freightPrice || 0.00}</View>
                            </View>
                        </View>

                    </>

                }
                <View className="order-item">
                    <View className="order-item-title">
                        <Icon name='bag-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                        打包费</View>
                    <View className="order-item-content">
                        <View className="order-item-empty">¥{packingFee || 0.00}</View>
                    </View>
                </View>
                <View className="order-item">
                    <View className="order-item-title">
                        <Icon name='phone-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                        联系方式</View>
                    <View className="order-item-content">
                        {/* <View className="order-item-empty">¥{packingFee || 0.00}</View> */}
                        <Input value={telephone} placeholder="-" style={{ textAlign: 'right' }} onInput={(e) => {
                            setTelephone(e.target.value)
                        }} />
                    </View>
                </View>
                <View className="order-item">
                    <View className="order-item-title">
                        <Icon name='search' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                        优惠口令</View>
                    <View className="order-item-content">
                        {/* <View className="order-item-empty">¥{packingFee || 0.00}</View> */}
                        <Input value={forward} placeholder="-" style={{ textAlign: 'right' }} onInput={(e) => {
                            setForward(e.target.value)
                        }} />
                    </View>
                </View>
                <View className="order-total">
                    <View className="order-total-title"></View>
                    <View className="order-total-price">
                        共 {goodsTotalNum}件商品   合计  ¥{orderTotal?.toFixed(2) || 0}
                    </View>
                </View>

            </View >
        </View >

        <View className="content-other order-item" onClick={() => {
            setRemarkShow(true)
        }}>
            <View className="order-item-title">备注</View>
            <View className="order-item-remark">{remarks || '口味、包装等特殊要求'}</View>
        </View>
        <View className="order-bottom" onClick={() => submitOrder()}>
            <View className="order-price">
                ¥{orderTotal?.toFixed(2) || 0}
            </View>
            <View className="order-btn">
                确认下单
            </View>
        </View>
        <RemarkModal remark={remarks} show={remarkShow} closeFn={(val) => {
            if (val) {
                setRemarks(val)
            }
            setRemarkShow(false);
        }} />
        {/* <PageContainer
            show={true}
            onLeave={() => {
                console.log("我离开了")
            }}
            onAfterLeave={() => {
                console.log("我离开了1")
            }}
        /> */}
    </View >)
})