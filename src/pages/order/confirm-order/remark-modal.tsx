import { Dialog } from "@antmjs/vantui"
import { Input, Textarea, View } from "@tarojs/components"
import { useEffect, useState } from "react"

export default ({ show, closeFn, remark }) => {
    const [value, setValue] = useState('')
    useEffect(() => {
        setValue(remark)
    }, [show])
    return (
        <Dialog
            id="vanDialog3"
            title="备注"
            showCancelButton
            // confirmButtonOpenType="getUserInfo"
            show={show}
            // onClose={() => closeFn(value)}
            onConfirm={() => {
                closeFn(value);
            }}
            onCancel={() => {
                closeFn()
            }}
        >
            <View style={{ padding: '0 20px' }}>
                <Textarea
                    placeholder="请输入备注要求"
                    value={value}
                    onInput={(e) => setValue(e.detail.value)}
                />
            </View>

        </Dialog>
    )
}