
page{
    background-color: #f1f2f3; 
}
.confirm-order{
    background-color: #f1f2f3;
    min-height: 100vh;
    // height: 100vh;
    // box-sizing: border-box;
    padding-bottom: calc(150px + env(safe-area-inset-bottom));
    .content{
        background-color:#a1a789;
        padding-top: 30px;
        .shop-rect{
            padding: 0 30px 20px 30px;
            &-name{
                font-size: 40px;
                line-height: 50px;
                color: #fff;
                .van-icon{
                    margin-right: 10px;
                }
            }
            &-desc{
                color: #fff;
                font-size: 28px;
                margin-top: 10px;
            }
        }
    }
    .content-other{
        background-color: #fff;
        margin-top: 20px;
        padding: 0 30px;
        margin-bottom:  calc(100px + env(safe-area-inset-bottom));
    }
}
.order-method{
    display: flex;
    width: 670px;
    box-sizing: border-box;
    flex-direction: row;
    margin: 0 40px;
    background-color: #8e976d;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    overflow: hidden;
    .method-item{
        border-top-left-radius: 20px;
        border-top-right-radius: 20px;
        flex: 1;
        color: #666;
        text-align: center;
        padding: 30px;
        font-size: 28px;
    }
    .selected{
       background-color: #fff;
       color: #333;
    }
}
.order-content{
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding: 0 40px 20px 40px;
    min-height: 500px;
    background-color: #fff;
   
    .order-total{
        width: 100%;
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        padding: 20px 0;
        .order-total-title{
            text-align: right;
            
        }
        .order-total-price{
            text-align: right;
            
        }
    }
}
 .order-item{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        min-height: 120px;
        padding: 20px 0;
        box-sizing: border-box;
        border-bottom: 1px solid #ccc;
       
        &-title{
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: row;
        }
        .address-detail{
            max-width: 300px;
            overflow: hidden;
            word-wrap: break-word;
            font-size: 24px;
        }
        &-price,&-empty,&-content{
            color: #666;
            font-size: 24px;
        }
        &-remark{
            color: #666;
            font-size: 24px;
        }
    }
.goods{
    padding: 20px 0;
    .goods-item{
        display: flex;
        flex-direction: row;
        padding: 20px 0;
        .goods-item-img{
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: inline-block;
        }
        .goods-item-content{
            padding-left: 20px;
            flex: 1;
            flex-direction: row;
            display: flex;
            align-items: center;
            justify-content: space-between;
            // background-color: red;
            .goods-item-left{
                .goods-item-name{
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-height: 80px;
                    white-space: normal;
                    display: -webkit-box;
                    word-break: break-all;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
                .goods-item-spe{
                    font-size: 24px;
                    width: 300px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    height: 40px;
                    line-height: 40px;
                    // margin-left: 10px;
                    white-space: pre;
                    color: #999;
                }
            }
            
            .goods-item-right{
                width: 160px;
                text-align: right;
            }
        }
    }
}

.order-bottom{
    position: fixed;
    right: 30px;
    // padding: ;
    height: 100px;
    display: flex;
    flex-direction: row;
    align-items: center;
    // justify-content: center;
    padding: 0 30px;
    color: #fff;
    border-radius: 50px;
    background-color:@primaryColor;
    bottom:  calc(30px + env(safe-area-inset-bottom));
    .order-price{
        font-size: 28px;
        margin-right: 20px;
    }
    .order-btn{
        font-size: 24px;
    }
}