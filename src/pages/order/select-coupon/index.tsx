import { View } from "@tarojs/components"
import './index.less'
import { useRouter } from "@tarojs/taro";
import api from "@/util/api";
import { useEffect, useState } from "react";
import CouponItem from "../../my/coupon/coupon-item";
import confirmOrderStore from "@/store/confirmOrder"
import { MiniBack } from "@/util/route";
import { Empty } from "@antmjs/vantui";

definePageConfig({
    navigationBarTitleText: '选择优惠券',
})
export default () => {
    const route = useRouter();
    const [list, setList] = useState([])
    // confirmOrder
    const getData = () => {
        api.getSelectCouponList({
            data: {
                couponUserId: route.params.couponUserId
            },
            method: 'get'
        }).then(res => {
            setList(res);
        })
    }

    useEffect(() => {
        getData();
    }, [])

    return (<View className="select-coupon">
        {
            list.map((item: any, index) =>
                <View className="" onClick={() => {

                    confirmOrderStore.setCoupon({
                        id: item.couponUserId,
                        couponUserId: item.couponUserId,
                        couponPrice: item.discount
                    });
                    MiniBack();
                }}>
                    <CouponItem data={item} key={index} />
                </View>
            )
        }
        {
            list.length === 0 && <Empty description="暂无可选优惠券" />
        }
    </View>)
}