import React, { useEffect, useState } from "react";
import { View, Input, Textarea, Switch } from "@tarojs/components";
import { Form, FormItem, Button, Icon } from "@antmjs/vantui";
import "./index.less";
import api from "@/util/api";
import { MiniBack } from "@/util/route";
import Taro, { useLoad, useRouter } from "@tarojs/taro";
import MiniToast from "@/util/toast";
import userStore from "@/store/user";
import MapFormItem from "@/components/map-form-item";

type RouterParams = {
    currentType?: string;
    type?: string;
    receiverInfo?: string;
    id: string;
};
const EditAddress: React.FC = () => {
    const formIt = Form.useForm();
    const { currentType, type, receiverInfo, id, businessType } = useRouter<RouterParams>().params;
    const [isDefault, setIsDefault] = useState(false);
    const [receiveUserName, setreceiveUserName] = useState("");
    const [mobile, setmobile] = useState("");
    const [region, setregion] = useState<any>();
    const [detail, setdetail] = useState("");
    const userInfo = userStore.getUser();

    useEffect(() => {
        Taro.setNavigationBarTitle({ title: currentType === "edit" ? "编辑地址" : "新增地址" });
    }, [currentType]);
    useEffect(() => {
        console.log(currentType, type, receiverInfo, id);
        id &&
            api
                .getaddressDetail({
                    data: { id: id, businessType: businessType || '' },
                    method: "post",
                })
                .then(res => {
                    setreceiveUserName(res.name);
                    setmobile(res.mobile);
                    setdetail(res.addressDetail);
                    setregion({
                        address: res.addressLocation,
                        latitude: res.latitude,
                        longitude: res.longitude,
                        name: res.addressName
                    });
                    setIsDefault(res.isDefault);
                });
    }, []);
    const save = () => {
        console.log("save", currentType);
        if (!receiveUserName) {
            return MiniToast.info("请填写收货人姓名");
        }
        if (!mobile) {
            return MiniToast.info("请填写手机号码");
        }
        if (!region) {
            return MiniToast.info("请选择省市区");
        }
        if (!detail) {
            return MiniToast.info("请填写详细地址");
        }
        if (!currentType || currentType === "add") {
            api.saveAddress({
                method: "POST",
                data: {
                    name: receiveUserName,
                    mobile,
                    addressLocation: region.address,
                    latitude: region.latitude,
                    longitude: region.longitude,
                    addressName: region.name,
                    addressDetail: detail,
                    isDefault,
                    businessType: businessType || ''
                },
            }).then(data => {
                saveSuccess(data);
            });
        } else if (currentType === "edit") {
            api.saveAddress({
                method: "POST",
                data: {
                    name: receiveUserName,
                    mobile,
                    addressLocation: region.address,
                    latitude: region.latitude,
                    longitude: region.longitude,
                    addressName: region.name,
                    addressDetail: detail,
                    isDefault,
                    id,
                    businessType: businessType || ''
                },
            }).then(() => {
                saveSuccess(id);
            });
        }
    };

    const del = () => {
        api.getDelete({
            method: "POST",
            data: {
                userId: userInfo.userId,
                id,
            },
        }).then(() => {
            MiniBack();
            MiniToast.success("删除成功");
        });
    };
    const saveSuccess = (addressId: string) => {
        MiniBack();
        MiniToast.success("保存成功");
    };
    const valueTrim = value => {
        return value.trim();
    };

    return (
        <View className="add-address">
            <View className="list_block">
                <View className="form-item">
                    <View className="form-label">收货人</View>
                    <View className="form-value">
                        <Input
                            value={receiveUserName}
                            placeholder="请输入收货人姓名"
                            onInput={e => setreceiveUserName(e.detail.value)}
                            onBlur={() => {
                                setreceiveUserName(valueTrim(receiveUserName));
                            }}
                        />
                    </View>
                </View>
                <View className="form-item">
                    <View className="form-label">手机号码</View>
                    <View className="form-value">
                        <Input
                            placeholder="请输入手机号码"
                            value={mobile}
                            maxlength={11}
                            type="number"
                            onInput={e => setmobile(e.detail.value)}
                            onBlur={() => {
                                setmobile(valueTrim(mobile));
                            }}
                        />
                    </View>
                </View>
                <View className="form-item">
                    <View className="form-label">所在地区</View>
                    <View className="form-value">
                        <MapFormItem value={region} onChange={(e) => {
                            console.log("MapFormItem:", e)
                            setregion(e);
                        }} />
                    </View>
                </View>
                <View className="form-item" style={{ alignItems: "flex-start" }}>
                    <View className="form-label" style={{ marginTop: "5px" }}>
                        详细地址
                    </View>
                    <View className="form-value">
                        <Textarea
                            value={detail}
                            onInput={e => {
                                setdetail(e.detail.value);
                            }}
                            placeholder="请输入详细地址信息"
                            maxlength={200}
                            onBlur={() => valueTrim("detail")}
                        />
                    </View>
                </View>
            </View>
            <View className="list_block">
                <View className="form-item">
                    <View className="form-label form-label-max">设为默认地址</View>
                    <View className="form-value">
                        <Switch checked={isDefault} onChange={() => setIsDefault(!isDefault)} />
                    </View>
                </View>
            </View>
            {currentType === "add" ? (
                <View
                    style={{ textAlign: "center", alignItems: "center", justifyContent: "center" }}
                    className="total"
                    onClick={save}>
                    保存
                </View>
            ) : (
                <View className="add-address-footer">
                    <View onClick={del}>删除地址</View>
                    <View onClick={save}>确认地址</View>
                </View>
            )}
        </View>
    );
};
export default EditAddress;


