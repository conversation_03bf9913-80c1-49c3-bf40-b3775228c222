page {
    height: 100%;
    background-color: #f1f2f3;
}

.add-address {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .vant-form-formItem-wrapper {
        background: transparent;
    }

    .list_block {
        margin-top: 20rpx;
        border-radius: 16rpx;
        background: #fff;
        margin-left: 24rpx;
        margin-right: 24rpx;
        .form-item {
            display: flex;
            align-items: center;
            padding: 30px 20px;
            flex-direction: row;
            border-bottom: 2px solid #f1f2f3;
            .form-label {
                margin-right: 20px;
                font-size: 32px;
                line-height: 40px;
                width: 160px;
                &-max {
                    width: 200px;
                }
            }
            .form-value{
                width: 100%;
                height:40px;
            }
        }
    }

    textarea {
        height: 90px;
        width: 450px;
        line-height: 40px;
        font-size: 28px;
        // padding: 8px 0;
    }
    .submit-btn {
        width: 600px;
        margin: 60rpx auto;
    }

    &-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        text-align: center;
        color: #fff;
        position: fixed;
        bottom: 24px;
        margin: 24px;
        padding: 24px;
        box-sizing: border-box;
        left: 0;
        right: 0;
        z-index: 2;
        > view {
            background: #4b5c10;
            width: 50%;
            border-radius: 60px;
            box-shadow: 0 3px 8px 0 rgba(255, 255, 255, 0.4);
            margin: 20px;
            padding: 20px;
            &:first-child {
                border: 1.5px solid #4b5c10;
                background: none;
                color: #4b5c10;
            }
        }
    }
}
