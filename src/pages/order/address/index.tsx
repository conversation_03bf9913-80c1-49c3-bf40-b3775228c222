import React, { useRef, useState } from "react";
import { View } from "@tarojs/components";
import {
    Empty,
    InfiniteScroll,
    InfiniteScrollProps,
    InfiniteScrollInstance,
    IPullToRefreshProps,
    PullToRefresh,
    Tag,
    Icon,
    pxTransform,
    Button,
    Cell,
} from "@antmjs/vantui";
import api from "@/util/api";
import { getStorage, setStorage } from "@/util/storage";

import "./index.less";
import Taro, { chooseAddress, useDidShow, useLoad, useRouter } from "@tarojs/taro";
// import { ADDRESS_ID, USER_INFO } from "@/util/constant";
import { MiniBack, MiniNavigateTo } from "@/util/route";
import MiniToast from "@/util/toast";
import { ADDRESS_ID } from "@/util/constant";
import userStore from "@/store/user";
import confirmOrderStore from "@/store/confirmOrder";
const AddressList: React.FC = () => {
    const userInfo = userStore.getUser();
    const pageSize = 20;
    const [record, setRecord] = useState<any[]>([]);
    const pageNo = useRef(1);
    const infiniteScrollInstance = useRef<InfiniteScrollInstance>();
    const { type, from, businessType } = useRouter().params;
    useDidShow(() => {

        pageNo.current = 1;
        infiniteScrollInstance.current?.reset(true);
        // console.log("api:", api)
        // api.getRegion({ methods: 'get', data: { pid: 0 } }).then((res) => {
        //     console.log(res);
        // })
    });

    const onRefresh: IPullToRefreshProps["onRefresh"] = () => {
        return new Promise(resolve => {
            pageNo.current = 1;
            infiniteScrollInstance.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore: InfiniteScrollProps["loadMore"] = () => {
        return new Promise(resolve => {
            getUserAddress()
                .then(data => {
                    let newData: any[];
                    if (pageNo.current === 1) {
                        newData = data;
                    } else {
                        newData = record.concat(data);
                    }
                    pageNo.current++;
                    setRecord(newData);
                    resolve(data.length < pageSize ? "complete" : "loading");
                })
                .catch(e => {
                    resolve("error");
                });
        });
    };

    const getUserAddress = () => {
        return api.getUserAddress({
            data: {
                userId: userInfo.userId,
                pageSize: pageSize,
                pageNo: pageNo.current,
                businessType: businessType ? businessType : ''
            },
        });
    };
    const edit = item => {
        MiniNavigateTo({
            url: `/pages/order/address/add-address/index?currentType=edit&type=${type}&id=${item.id}&businessType=${businessType || ""}`,
        });
    };
    const tapSelAddress = item => {
        confirmOrderStore.setAddress(item);
        setStorage(ADDRESS_ID, item.id);
        Taro.navigateBack();
    };
    const getWeiXinAddress = () => {
        chooseAddress({
            success: res => {
                api.saveAddress({
                    method: "POST",
                    data: {
                        userId: userInfo.userId,
                        receiveUserName: res.userName,
                        mobile: res.telNumber,
                        detail: res.detailInfo,
                        paperNo: "",
                        provinceId: -1,
                        cityId: -1,
                        regionId: -1,
                        province: res.provinceName,
                        city: res.cityName,
                        region: res.countyName,
                    },
                    filterCheck: true,
                }).then(data => {
                    if (type === "order") {
                        setStorage(ADDRESS_ID, data);
                        MiniBack({ delta: 1 });
                    }
                    MiniToast.success("保存成功");
                });
            },
            fail: function (err) {
                console.log(err);
            },
        });
    };

    const selectAddress = (item: any) => {
        confirmOrderStore.setAddress(item);
        if (from === 'order') {
            MiniBack();
            return;
        }
        MiniNavigateTo({
            url: "/pages/cart/cart",
        });
    };

    useDidShow(() => { });
    return (
        <View className="address-list">
            <PullToRefresh onRefresh={onRefresh}>
                <View className="address-list-wrapper">
                    <View className="list">
                        <View className="items">
                            {record?.map((item, index) => {
                                return (
                                    <View
                                        className={`item ${item.isDefault ? "selected" : ""}`}
                                        key={item.id}
                                    // onClick={() => tapSelAddress(item)}
                                    >
                                        <View className={"address-wrapper"}>
                                            <View className="adr_info">
                                                {item.isDefault && (
                                                    <Tag color="#F50050" className={"default"}>
                                                        默认
                                                    </Tag>
                                                )}
                                                <View className="address">
                                                    {item.province} {item.city} {item.region}
                                                </View>
                                            </View>
                                            <View className="address_detail">
                                                {item.addressLocation}
                                                {
                                                    type === "select" &&
                                                    <Icon
                                                        name="edit"
                                                        className="icon"
                                                        size="24px"
                                                        style={{ marginLeft: "10px" }}
                                                        onClick={() => edit(item)}
                                                    />
                                                }
                                            </View>
                                            <View className="usr_info">
                                                <View className="usr_name">{item.name}</View>
                                                <View className="usr_tel">{item.mobile}</View>
                                            </View>
                                        </View>
                                        {type === "select" ? (
                                            <View
                                                onTap={event => {
                                                    event.stopPropagation();
                                                    selectAddress(item);
                                                }}
                                                className={"edit"}>
                                                选择
                                            </View>
                                        ) : (
                                            <View
                                                onTap={event => {
                                                    event.stopPropagation();
                                                    edit(item);
                                                }}
                                                className={"edit"}>
                                                <Icon name="edit" color={"#CBCCCD"} size={pxTransform(32)} />
                                            </View>
                                        )}
                                    </View>
                                );
                            })}
                        </View>
                        <InfiniteScroll
                            loadMore={loadMore}
                            ref={infiniteScrollInstance}
                            completeText={
                                <>
                                    {record.length == 0 ? (
                                        <Empty
                                            description="暂无数据！"
                                            image="https://dante-img.oss-cn-hangzhou.aliyuncs.com/55439458101.png"
                                        />
                                    ) : (
                                        "没有更多了"
                                    )}
                                </>
                            }
                        />
                    </View>
                </View>
            </PullToRefresh>
            <Button
                className="total"
                onClick={() => MiniNavigateTo({ url: `/pages/order/address/add-address/index?currentType=add&businessType=${businessType || ""}` })}>
                <Icon name={"plus"} color={"#fff"} /> 添加收货地址
            </Button>
        </View>
    );
};
export default AddressList;
