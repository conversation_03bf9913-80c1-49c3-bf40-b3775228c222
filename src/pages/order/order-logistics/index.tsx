import { Steps } from "@antmjs/vantui";
import { View, Text } from "@tarojs/components";
import "./index.less";
import { useEffect, useState } from "react";
import api from "@/util/api";
import { useRouter } from "@tarojs/taro";
import Taro from "@tarojs/taro";
import MiniToast from "@/util/toast";
// import Min from "@/utils/toast";

definePageConfig({
    // navigationStyle: "custom",
    navigationBarTitleText: "物流详情",
    // navigationBarTextStyle: "white"
});
export default () => {
    const route = useRouter();
    const [detail, setDetail] = useState({ expNo: "", comName: "" });
    const [list, setList] = useState([]);
    // freightType
    const getWaybillRoute = () => {
        api.getWaybillRoute({
            method: 'POST',
            data: {
                orderId: route.params.id,

            }
        }).then(res => {
            console.log("res:", res)
            setList(res.map((item) => {
                return {
                    text: item.acceptTime,
                    desc: item.remark
                }
            }));
            if (res[0]) {
                setDetail({
                    mailNo: res[0].mailNo,
                })
            }
        })
    }

    useEffect(() => {
        getWaybillRoute();
    }, [])
    return (
        <View className="logistics">
            <View className="logistics-head">
                <View
                    className="logistics-head-item"
                    onClick={() => {
                        Taro.setClipboardData({
                            data: detail.mailNo,
                            success: function (res) {
                                MiniToast.info("复制成功");
                            },
                        });
                    }}
                    style={{ marginBottom: "24rpx" }}
                >
                    物流单号：{detail.mailNo} <Text className="copy">复制</Text>
                </View>
                <View className="logistics-head-item">物流公司：顺丰</View>
            </View>
            <View className="list">
                <View className="list-title">订单跟踪</View>
                <View className="list_content">
                    <Steps steps={list} active={0} direction="vertical" activeColor="#ee0a24" />
                </View>
            </View>
        </View>
    )
}