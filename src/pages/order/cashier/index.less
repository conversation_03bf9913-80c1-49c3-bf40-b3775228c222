.cashier-page{
    width: 100vw;
    // height: 100vh;
    padding-top: 200px;
    padding-bottom: calc(140px + env(safe-area-inset-bottom));;
    background-color: #f1f2f3;
    &__header{
        width: 100%;
        padding: 100px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .shop-name{
            font-size: 32px;
            color: #ccc;
        }
        .total-price{
            // font-size: 40px;
            // color: #333;
            display: flex;
            flex-direction: row;
            align-items: flex-end;
            padding: 18px;
            &-babel{
                font-size: 24px;
                line-height: 1em;
                margin-bottom: 5px;
            }
            &-value{
                font-size: 50px;
                line-height: 1em;
            }
        }
    }

    .goods-rect{
         margin: 0px 30px 30px  30px;
         background-color: #fff;
         border-radius: 10px;
         padding: 30px 30px 10px 30px;
         &-title{
            padding-bottom: 20px;
         }
         &-other{
            border-top: 1px solid #f4f4f4;
            text-align: right;
            padding-top: 40px;
            padding-bottom: 40px;
            &-text1{
                font-size: 30px;
                color: #999;
            }
            &-text2{
                margin-left: 8px;
                font-size: 36px;
                color: #333;
            }
         }
        .shop-item-list .goods-list .good-item {
            width: 100%;
            // margin: 0;
            margin-left: 0;
            margin-right: 0;
        }
    }
    .payment{
        margin: 30px;
        margin-bottom:  calc(120px + env(safe-area-inset-bottom));;
        &-item{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 50px 30px;
            &-left{
                display: flex;
                align-items: center;
                justify-content: center;
                .icon{
                    margin-right: 10px;
                }
            }
        }
        &-wx{
            margin-top: 30px;
            background-color: #fff;
            // padding: 30px;
            border-radius: 10px;
        }
    } 
    .bottom-btn{
        position: fixed;
        bottom: calc(20px + env(safe-area-inset-bottom));
        left: 30px;
        right: 30px;
        background-color: @primaryColor;
        border-radius: 44px;
        height: 88px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
    }
    .van-icon-arrow-left:before {
        content: "\e668";
        color: #000;
    }
}