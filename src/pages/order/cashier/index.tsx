import api from "@/util/api";
import { View, Text, } from "@tarojs/components"
import { useRouter } from "@tarojs/taro"
import { useEffect, useState } from "react";
import { MiniObject } from "src/type/common";
import './index.less'
import GoodsList from "@/components/goods-list";
import { Icon, Checkbox } from "@antmjs/vantui";
import Taro from "@tarojs/taro";
import { MiniBack, MiniNavigateTo, MiniReLaunch, MiniRedirectTo } from "@/util/route";
import MiniToast from "@/util/toast";

import { NavBar, MiniNavBar } from '@antmjs/vantui'
import order from "../order";
definePageConfig({
    navigationBarTitleText: '收银台',
    navigationStyle: 'custom'
})

export default () => {
    const route = useRouter();
    const { orderNo, coldChainOrderId, commonOrderId } = route.params;
    const [detail, setDetail] = useState<MiniObject>({});
    const [detail1, setDetail1] = useState({});
    let [total, setTotal] = useState(0)
    const [paymentType, setPaymentType] = useState('wxpay');
    let [discountPrice, setdiscountPrice] = useState(0);
    let [freightPrice, setfreightPrice] = useState(0)
    let [packingFee, setpackingFee] = useState(0);
    let [actualPrice, setactualPrice] = useState(0)

    const getOrder = (id, once) => {
        // if(once)
        api.getOrderdetail({
            data: {
                orderId: id || 235
            },
            method: 'POST',
            showLoad: true,
        }).then((res) => {
            console.log(res);
            if (!once || once === 1) {
                setDetail(res);
            }
            if (once === 2) {
                setDetail1(res);
            }
            //  = 0
            // if (once === 2) { }
            res.goodsList.map((item) => {
                total += item.number;
            })
            discountPrice += res.discountPrice;
            actualPrice += res.actualPrice
            packingFee += res.packingFee;
            freightPrice += res.freightPrice;
            setfreightPrice(freightPrice)
            setdiscountPrice(discountPrice);
            setactualPrice(actualPrice);
            setpackingFee(packingFee);
            setTotal(total);
        })
    }

    const getWxPayParams = (orderId: string) => {
        api.getWxPayParams({
            data: {
                orderId: orderId
            },
            method: 'POST',
        }).then(res => {
            console.log("res:", res)
            const payObj = res;
            Taro.requestPayment({
                //@ts-ignore
                appId: payObj.appId,
                timeStamp: payObj.timeStamp,
                nonceStr: payObj.nonceStr,
                package: payObj.packageValue,
                signType: payObj.signType,
                paySign: payObj.paySign,
                success: (successRes) => {
                    MiniToast.success('支付成功')
                    MiniNavigateTo({
                        url: '/pages/order/order-detail/index?orderNo=' + orderId
                    });
                },
                fail(err) {
                    console.log(err)
                    MiniToast.error('支付失败');
                    MiniNavigateTo({
                        url: '/pages/order/order-detail/index?orderNo=' + orderId
                    });
                }
            })
        }).catch((err) => {
        })
    }

    const wxShopPayParams = (commonOrderId, coldChainOrderId) => {
        api.getWxShopPayParams({
            data: {
                commonOrderId,
                coldChainOrderId,
            },
            method: 'POST',
        }).then(res => {
            console.log("res:", res)
            const payObj = res;
            Taro.requestPayment({
                //@ts-ignore
                appId: payObj.appId,
                timeStamp: payObj.timeStamp,
                nonceStr: payObj.nonceStr,
                package: payObj.packageValue,
                signType: payObj.signType,
                paySign: payObj.paySign,
                success: (successRes) => {
                    MiniToast.success('支付成功')
                    MiniNavigateTo({
                        url: '/pages/order/order-detail/index?orderNo=' + orderId
                    });
                },
                fail(err) {
                    console.log(err)
                    MiniToast.error('支付失败');
                    MiniNavigateTo({
                        url: '/pages/order/order-detail/index?orderNo=' + orderId
                    });
                }
            })
        }).catch((err) => {
        })
    }


    const payByBalance = (orderId) => {
        api.payOrderByBalance({
            data: {
                orderId: orderId
            },
            method: 'POST',
        }).then((res) => {
            MiniToast.success('抵扣成功')
            MiniNavigateTo({
                url: '/pages/order/order-detail/index?orderNo=' + orderId
            });
        })
    }

    const payOrdersByBalance = (id1, id2) => {
        api.payOrdersByBalance({
            data: {
                coldChainOrderId: id1,
                commonOrderId: id2,
            },
            method: 'POST',
        }).then(res => {
            console.log("res", res);
            MiniToast.success('抵扣成功')
            MiniReLaunch({ url: "/pages/container/index?select=1" })
        })
    }


    useEffect(() => {
        if (orderNo) {
            getOrder(orderNo, 1);
        }
        if (commonOrderId) {
            getOrder(commonOrderId, 1);
        }
        if (coldChainOrderId) {
            getOrder(coldChainOrderId, 2);
        }
    }, [])

    return (<View className="cashier-page">
        <NavBar
            title="收银台"
            leftArrow
            style={"position: fixed;left: 0;right: 0;top: 0;"}
            onClickLeft={() => {
                MiniToast.showModal({
                    title: '确定返回吗',
                    success: (res) => {
                        if (res.confirm)
                            MiniReLaunch({ url: "/pages/container/index?select=1" })
                    }
                })
            }}
        />
        <View className="cashier-page__header">
            <View className="shop-name">7RiverLight</View>
            <View className="total-price">
                <Text className="total-price-babel">¥</Text><Text className="total-price-value">{actualPrice || 0}</Text>
            </View>
        </View>
        <View className="goods-rect">
            <View className="goods-rect-title">
                订单详情
            </View>
            <View>
                <GoodsList list={detail.goodsList || []} />
                {
                    detail1 && detail1.goodsList &&
                    <GoodsList list={detail1.goodsList || []} />
                }
            </View>

            <View className="goods-rect-other">
                {
                    freightPrice > 0 &&
                    <View className="goods-rect-other-text1">运费:¥{freightPrice || 0}</View>
                }
                {
                    discountPrice > 0 &&
                    <View className="goods-rect-other-text1">优惠金额:¥{discountPrice || 0}</View>

                }

                <View className="goods-rect-other-text1">打包费:¥{packingFee || 0}</View>

                <Text className="goods-rect-other-text1">共{total}件商品 小记</Text>
                <Text className="goods-rect-other-text2">¥{actualPrice || 0}</Text>
            </View>
        </View>
        <View className="payment">
            <View className="payment-title">
                请选择支付方式
            </View>
            <View className="payment-wx payment-item">
                <View className="payment-item-left">
                    <Icon name="wechat" className="icon" color={"#85cc44"} size="50" />
                    微信支付
                </View>
                <Checkbox value={paymentType === 'wxpay'} onChange={(e) => {
                    setPaymentType('wxpay')
                }} />
            </View>
            <View className="payment-wx payment-item">
                <View className="payment-item-left">
                    <Icon name="card" className="icon" color={"#ff9800"} size="50" />
                    使用余额
                </View>
                <Checkbox value={paymentType === 'jifen'} onChange={(e) => {
                    setPaymentType('jifen')
                }} />
            </View>
        </View>
        <View className="bottom-btn" onClick={() => {
            if (paymentType === 'wxpay') {
                if (commonOrderId && coldChainOrderId) {
                    wxShopPayParams(commonOrderId, coldChainOrderId)
                    return
                }
                getWxPayParams(orderNo || commonOrderId || coldChainOrderId)

            } else {
                // MiniToast.info('开发中')       &
                if (commonOrderId && coldChainOrderId) {
                    payOrdersByBalance(coldChainOrderId, commonOrderId);
                    return
                }
                if (commonOrderId) {
                    payByBalance(commonOrderId as string)
                    return
                }
                if (coldChainOrderId) {
                    payByBalance(coldChainOrderId as string)
                    return
                }
                payByBalance(orderNo as string)
            }
        }}>
            {paymentType === 'wxpay' ? '微信支付' : '余额支付'}¥{(actualPrice || 0).toFixed(2)}
        </View>
    </View>)
}