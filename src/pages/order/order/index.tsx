import { View } from "@tarojs/components"
import Header from "./header"
import './index.less'
import { Empty, Button, PullToRefresh, InfiniteScroll } from '@antmjs/vantui'
import { useDidShow, useRouter } from "@tarojs/taro"
import { useEffect, useRef, useState } from "react"
import api from "@/util/api"
import OrderItem from "./order-item"
import { MiniNavigateTo } from "@/util/route"

export default () => {
    const route = useRouter();
    const [data, setData] = useState<any[]>([]);
    const infiniteScrollRef = useRef<any>();
    const pageSize = 10;
    const pageNo = useRef(1);

    const orderType = useRef(2)
    const todayOrHistory = useRef(1)

    const getOrderDatas = () => {

        return api.getOrderList({
            data: {
                currentPage: pageNo.current || 1,
                pageSize: pageSize,
                orderType: orderType.current, // 订单类型，1为正常订单，3为积分订单
                todayOrHistory: todayOrHistory.current,
            },
            method: 'POST',
            showError: false,
            showLoad: false,
            filterCheck: true,
        });
    };
    const onRefresh = () => {
        pageNo.current = 1;
        return new Promise(async (resolve: (value: undefined) => void) => {
            infiniteScrollRef.current?.reset();
            try {
                const result = await getOrderDatas();
                pageNo.current++;
                setData(result.data || []);
                console.log("newata:", result.data);
            } catch (error) {
                console.log("error:", error);
            }
            infiniteScrollRef.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore = () => {
        return new Promise(async (resolve: (value: "loading" | "error" | "complete") => void) => {
            try {
                const result = await getOrderDatas();
                let newData: any[] = [];
                if (pageNo.current === 1) {
                    newData = result.data || [];
                } else {
                    newData = data.concat(result.data || []);
                }
                pageNo.current++;
                setData(newData);
                //  endStatus.current = newData.length >= result.total;
                resolve(newData.length >= result.count ? "complete" : "loading");
            } catch (error) {
                resolve("error");
            }
        });
    };

    useDidShow(() => {
        pageNo.current = 1;
        infiniteScrollRef.current?.reset(true);
    });
    return (<View className="order">
        <Header onChange={(data) => {
            console.log(data);
            orderType.current = data.orderType
            todayOrHistory.current = data.timeType
            pageNo.current = 1;
            infiniteScrollRef.current?.reset(true);
        }} />
        <PullToRefresh onRefresh={onRefresh} style={{ backgroundColor: '#f1f2f3' }}>
            <View className="order-list">
                {data.map((item) => <OrderItem data={item} key={item.id} load={() => {
                    pageNo.current = 1;
                    infiniteScrollRef.current?.reset(true);
                }} />)}
            </View>
            <InfiniteScroll
                completeText={
                    <>
                        {data.length == 0 ? (
                            <Empty description="暂无订单" >
                                <Button round={true} className="bottomButton"
                                    onClick={() => {
                                        MiniNavigateTo({ url: '/pages/cart/cart' })
                                    }}
                                >
                                    去点单
                                </Button>
                            </Empty>
                        ) : (
                            "没有更多了"
                        )}
                    </>
                }
                loadMore={loadMore}
                ref={infiniteScrollRef}
            />
        </PullToRefresh>
    </View>)
}