.order-header{
    width: 100%;
    background-color: #fff;
    top: 0;
    z-index: 200;
    position: sticky;
    box-shadow: 0px 0px 5px #ccc;
    .time-tab{
        display: flex;
        height: 100px;
        flex-direction: row;
        position: relative;
        align-items: center;
        justify-content:space-between;
        width: 100%;  
        border-bottom: 1px solid #f5f6f7;
        background-image: url(https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/order_list_head.jpg);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        .time-tab-item{
            flex: 1;
            text-align: center;
        }
        .active{

        }
        .active-line{
            position: absolute;
            bottom: 0;
            // left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 4px;
            background-color: #333;
        }
    }
    .type-tab{
        display: flex;
        height: 100px;
        flex-direction: row;
        position: relative;
        align-items: center;
        justify-content:space-between;
        width: 100%; 
        .type-tab-item{
            flex: 1;
            text-align: center;
            color: #ccc;
            .active{
                color: #333;
            }
        } 
    }
}