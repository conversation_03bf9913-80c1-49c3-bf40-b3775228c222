.order-item{
    margin: 20px;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px 20px;
    .shop-item-list .goods-list .good-item {
        width: 100%;
        // margin: 0;
        margin-left: 0;
        margin-right: 0;
    }
    .order-item-head{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 0;
        .order-item-no{
            font-size: 36px;
            line-height: 40px;
            font-weight: 700;
            color:@primaryColor ;
        }
        .order-item-status{
            font-size: 28px;
            line-height: 40px;
        }
    }
    .order-item-other{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 12px;
    }
    .order-item-btn{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        // flex-direction: row-reverse;
        padding-bottom: 20px;
        .btn{
            display: inline-flex;
            height: 64rpx;
            background: #ffffff;
            border-radius: 32rpx;
            border: 2rpx solid #cccccc;
            margin-right: 24rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 36rpx;
            padding: 14rpx 32rpx;
            box-sizing: border-box;
            border-radius: 32rpx;
            // background-color: @primaryColor;
            // color: #fff;
        }
        .btn1{
            background-color: @primaryColor;
            color: #fff;
        }
    }
}