import { MiniNavigateTo } from "@/util/route"
import { View, Button } from "@tarojs/components"
import './order-item.less';
import GoodsList from "@/components/goods-list";
import { Icon } from "@antmjs/vantui";
import OrderBtns from "../order-detail/order-btns";
import QrCodeModal from "../../home-detail/qrCode-modal";
export default ({ data, load }) => {
    // actualPrice: 61.26
    // addTime: "2024-03-29 23:32:22"
    // address: "安徽省 合肥市 包河区 312312312312安师大"
    // consignee: "件到3213运写"
    // freightPrice: 0
    // goodsList: [,…]
    // goodsPrice: 61.26
    // groupin: false
    // handleOption: {cancel: true, delete: false, pay: true, comment: false, confirm: false, refund: false, rebuy: false,…}
    // id: 222
    // mobile: "18174278552"
    // orderSn: "20240329911777"
    // orderStatusText: "未付款"
    return (<View className="order-item" onClick={() => {
        MiniNavigateTo({
            url: '/pages/order/order-detail/index?orderNo=' + data.id
        });
    }}>
        <View className="order-item-head">
            <View className="order-item-no">
                {/* S{data.id} */}
                {data?.mealCode}
            </View>
            <View className="order-item-status">
                {data.orderStatusText}
                <Icon name="arrow" />
            </View>
        </View>
        <View className="order-item-other">
            <View className="order-shop-name">
                {data.shopName || '7RiverLight'}
            </View>
            <View className="order-item-tiem">
                {data.addTime}
            </View>
        </View>
        <GoodsList list={data.goodsList || []} />
        <View className="order-item-btn" >
            <OrderBtns handleOption={data?.handleOption} orderNo={data?.id}
                load={() => {
                    // l(data?.id)
                    load();
                }}
            />
            <View>
                {
                    data.showMealQrCode && data.mealQrCode && <QrCodeModal renderLeft={({ openFn }) => {
                        return (<Button className='btn btn1' onClick={e => {
                            e.stopPropagation();
                            openFn()
                        }}>取餐码</Button>)
                    }} data={{
                        mealCode: data.mealCode,
                        mealQrCode: data.mealQrCode,
                        orderId: data.id
                    }} />
                }
            </View>

        </View>
    </View>)
}