import { View } from "@tarojs/components"
import { useState } from "react";
import './header.less';
export default ({ onChange }) => {
    const [isCurrent, setCurrent] = useState(true);
    const [orderType, setOrderType] = useState(2);
    const list = [
        {
            label: '全部',
            type: 2
        },
        {
            label: '堂食订单',
            type: 1
        },
        {
            label: '外送订单',
            type: 0
        },
        {
            label: '商城订单',
            type: 3
        },
    ]
    return (<View className="order-header">
        <View className="time-tab">
            <View className={isCurrent ? 'time-tab-item  active' : ' time-tab-item'} onClick={() => { setCurrent(true); onChange({ timeType: 1, orderType }) }}>
                今日订单
            </View>
            <View className={!isCurrent ? 'time-tab-item active' : ' time-tab-item'} onClick={() => { setCurrent(false); onChange({ timeType: 0, orderType }) }}>
                历史订单
            </View>
            <View className="active-line" style={{ left: `${isCurrent ? 25 : 75}%` }} />
        </View>
        <View className="type-tab">
            {
                list.map((item) => {
                    return (<View key={item.label} className="type-tab-item">
                        <View className={item.type === orderType ? 'active' : ''}
                            onClick={() => {
                                setOrderType(item.type);
                                onChange({ timeType: isCurrent ? 1 : 0, orderType: item.type })
                            }}>
                            {item.label}
                        </View>
                    </View>)
                })
            }
        </View>
    </View>)
}