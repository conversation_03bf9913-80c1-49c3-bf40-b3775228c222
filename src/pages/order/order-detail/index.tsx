import GoodsList from "@/components/goods-list"
import api from "@/util/api"
import { timestampToTime } from "@/util/common"
import MiniToast from "@/util/toast"
import { Cell, Icon } from "@antmjs/vantui"
import { View, Text } from "@tarojs/components"
import Taro from "@tarojs/taro"
import { useRouter } from "@tarojs/taro"
import './index.less'
import { useEffect, useState } from "react"
import { MiniNavigateTo } from "@/util/route"
import OrderBtns from "./order-btns"
definePageConfig({
    navigationBarTitleText: '订单详情'
})
export default () => {
    const [detail, setDetail] = useState<any>()
    const [mailObj, setMailObj] = useState()
    const route = useRouter()
    const getDetail = (id) => {
        api.getOrderdetail({
            data: {
                orderId: id
            },
            method: 'POST',
            showLoad: true,
        }).then((res) => {
            console.log(res);
            setDetail(res);
            if ([3, 4].includes(res.freightType)) {
                getWaybillRoute();
                // MiniNavigateTo({
                //     url: `/pages/order/order-logistics/index?id=${id}`
                // })
            }
        })
    }

    const getWaybillRoute = () => {
        api.getWaybillRoute({
            method: 'POST',
            data: {
                orderId: route.params.orderNo,

            },

        }).then(res => {
            console.log("res:", res)
            if (res && res.length) {
                const obj = {
                    mailRemark: res[0].remark,
                    mailTime: res[0].acceptTime,
                    mailNo: res[0].mailNo
                }
                setMailObj(obj)
                console.log("remark:", obj)
            }
        })
    }


    useEffect(() => {
        getDetail(route.params.orderNo);
    }, [])
    return (<View className="order-detail">
        {
            detail && (<View className="order-content">
                <View className="order-main-card">
                    <View className="order-status-text">
                        {detail?.orderStatusText}
                    </View>
                    <View className="order-main-no">
                        {/* S{detail?.id} */}
                        {detail?.mealCode}
                    </View>

                    <View className="order-main-point">
                        <Icon name="miniprogram-o" size="50" className="order-main-point-icon" color="#000" />
                        <View >
                            <View className="order-main-point-title">积分</View>
                            <View className="order-main-point-value">{detail?.point || 0.0}</View>
                        </View>
                    </View>
                </View>
                {
                    [3, 4].includes(detail?.freightType) &&
                    <View className="row-item" >
                        <Icon name='logistics' size="32px" />
                        {
                            mailObj?.mailNo ?
                                <View className="row-item-content"
                                    onClick={() => {
                                        MiniNavigateTo({
                                            url: `/pages/order/order-logistics/index?id=${route.params.orderNo}`
                                        })
                                    }}
                                >

                                    <View onClick={(e) => {
                                        Taro.setClipboardData({
                                            data: detail.mailNo,
                                            success: function (res) {
                                                MiniToast.info("复制成功");
                                            },
                                        });
                                        e.stopPropagation();
                                    }}>
                                        快递单号 {mailObj?.mailNo}
                                        <Icon
                                            classPrefix={"iconfont"}
                                            style={{ fontSize: "30rpx", display: 'inline-block' }}
                                            name={" icon-file-copy-line"}
                                            size={40}
                                            color={"#999"}
                                        >

                                        </Icon>
                                    </View>
                                    <View >
                                        {mailObj?.mailTime}
                                    </View>
                                    <View className="order-new-logistics">
                                        {mailObj?.mailRemark}
                                    </View>
                                </View>
                                :
                                <View className="row-item-content">
                                    <Text>暂无运单轨迹</Text>
                                </View>
                        }

                    </View>
                }
                {
                    [3, 4, 0].includes(detail?.freightType) && detail?.address &&
                    <View className="order-logistics-card">
                        收货地址：  {detail?.address}
                    </View>
                }
                <View className="order-good-card">
                    <View className="order-good-title">我的订单</View>
                    <View className="goods-block">
                        <GoodsList list={detail?.goodsList || []} />
                    </View>
                    <View className="info-block">
                        <Cell className="info-item" title="商品总额" border={false} value={`¥${detail.goodsPrice || 0}`} />
                        <Cell className="info-item" title="运费" border={false} value={`¥${detail.freightPrice || 0}`} />
                        <Cell className="info-item" title="优惠" border={false} value={`-¥${detail.discountPrice || 0}`} />
                        <Cell className="info-item" title="打包费" border={false} value={`¥${detail?.packingFee || 0}`} />
                    </View>
                    <View className="goods-total">
                        <Text className="price-title">
                            实际支付：
                            <Text className="price-value">￥{detail.actualPrice}</Text>
                        </Text>
                    </View>
                </View>
                <View className="order-card">
                    <View className="order-title">订单信息</View>
                    <Cell
                        className="info-item"
                        title="订单编号:"
                        border={false}
                        value={`${detail.orderSn}`}
                        renderRightIcon={
                            <Icon
                                classPrefix={"iconfont"}
                                style={{ fontSize: "30rpx" }}
                                name={" icon-file-copy-line"}
                                size={40}
                                color={"#999"}
                                onClick={() => {
                                    Taro.setClipboardData({
                                        data: detail.orderId,
                                        success: function (res) {
                                            MiniToast.info("复制成功");
                                        }
                                    });
                                }}
                            />
                        }
                    />
                    <Cell
                        className="info-item"
                        title="下单时间:"
                        border={false}
                        value={`${timestampToTime(detail.createdAt, "YYYY-MM-DD HH:mm:ss")}`}
                    />
                    <Cell
                        className="info-item"
                        title="支付方式:"
                        border={false}
                        value={`${detail.payType}`}
                    />
                    <Cell
                        className="info-item"
                        title='店铺电话:'
                        border={false}
                        value={`${detail.shopPhone}`}
                        onClick={() => {
                            Taro.makePhoneCall({
                                phoneNumber: detail.shopPhone,
                            })
                        }}
                    />
                </View>
                <View className="order-bottom">
                    <View className="btn-container">
                        <OrderBtns handleOption={detail?.handleOption} orderNo={detail?.id}
                            load={() => {
                                getDetail(detail?.id)
                            }}
                        />
                    </View>
                </View>
            </View >)
        }
    </View >)
}