.order-bg-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 140rpx;
    border-bottom-left-radius: 30rpx;
    border-bottom-right-radius: 30rpx;
    background-color: #f50050;
    z-index: 1;
}
.page-title {
    color: #fff;
    z-index: 999;
    background-color: #f50050;
    .van-nav-bar__content {
        height: 88rpx;
    }
    .van-icon-arrow-left:before {
        content: "\e668";
        color: #fff;
    }
    .van-nav-bar__title {
        color: #fff;
    }
}

page {
    position: relative;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f1f2f3;
   
} .order-content {
        z-index: 10;
        position: relative;
        padding-bottom: calc(110px + env(safe-area-inset-bottom));
        .row-item{
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 20px 44px;
            .row-item-content{
                margin-left: 18px;
            }
            .order-new-logistics{
                white-space: nowrap; /* 禁止换行 */
                overflow: hidden; /* 超出部分隐藏 */
                text-overflow: ellipsis; /* 超出部分显示省略号 */
                height: 40px;
                width: 500rpx;
            }
        }
        .order-main-card {
            margin: 20rpx 24rpx 40rpx 24rpx;
            padding: 0 32rpx;
            // min-height: 200px;
            border-radius: 16rpx;
            padding-top: 30px;
            position: relative;
            .order-status-text {
                font-size: 44px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #333333;
                line-height: 50rpx;
            }
            .order-main-no{
                margin-top: 20px;
                font-size: 50px;
                line-height: 60px;
                font-weight: 700;
                color:@primaryColor ;
            }
            .order-main-point{
                position: absolute;
                right: 50px;
                top: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: row;
                &-title{
                    font-size: 28px;
                    color:#333 ;
                }
                &-icon{
                    padding: 10px;
                    border-radius: 50%;
                    margin-right: 20px;
                    background-color: #fff;
                }
                &-value{
                    font-size: 32px;
                    margin-top: 10px;
                    color:@primaryColor ;
                }
            }
        }
        .order-logistics-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 24px;
            background-color: #fff;
            // min-height: 200px;
            border-radius: 16rpx;
            .card-title {
                font-size: 32rpx;
                font-weight: 500;
                padding-bottom: 24rpx;
            }
            .logistic_title {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                // margin: 15rpx 0;
                .logistic_title_list {
                    font-size: 24rpx;
                    // margin-right: 20rpx;
                    color: #3a3a3a;
                    border: 1px solid #cccccc;
                    padding: 5rpx;
                    border-radius: 5rpx;
                }
                .active {
                    color: #f51214;
                    border: 1px solid #f51214;
                }
            }
            .info_block {
                // margin-top: 20rpx;
                background: #fff;
                // padding: 0 36rpx 36rpx 36rpx;
                .item {
                    height: 58rpx;
                    line-height: 58rpx;
                    // border-bottom: 1rpx solid #cccccc;
                    display: flex;
                    justify-content: space-between;
                    font-size: 24rpx;
                    .title {
                        color: #333333;
                        font-size: 24rpx;
                        font-weight: 400;
                    }
                    .total_price {
                        color: #333333;
                    }
                    .cost {
                        color: #333333;
                    }
                    .tatal {
                        font-weight: 400;
                        color: #333333;
                        font-size: 24rpx;
                    }
                    .arrow {
                        width: 20rpx;
                        height: 20rpx;
                    }
                }
            }
        }
        .order-good-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 32px 32rpx 0 32px;
            background-color: #fff;
            min-height: 200px;
            border-radius: 16rpx;
            .shop-item-list .goods-list .good-item {
                width: 100%;
                // margin: 0;
                margin-left: 0;
                margin-right: 0;
            }
            .order-good-title{
                font-size: 32rpx;
                font-weight: 500;
                padding-bottom: 24rpx;
            }
            .info-block {
                .info-item {
                    padding: 20rpx 0;
                    line-height: 30rpx;
                }
            }
            .goods-total {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                padding-bottom: 32rpx;
                .price-title {
                    text-align: right;
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 24rpx;
                }
                .price-value {
                    font-size: 32rpx;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: @secondaryColor;
                    line-height: 32rpx;
                }
            }
        }
        .order-payment-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 32rpx 32rpx;
            background-color: #fff;
            min-height: 200px;
            border-radius: 16rpx;
            .payment-title {
                font-size: 32rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 32rpx;
                padding-bottom: 20rpx;
            }
            .info-item {
                padding: 20rpx 0;
                line-height: 30rpx;
            }
        }
        .order-card {
            margin: 20rpx 24rpx 20rpx 24rpx;
            padding: 32rpx 32rpx;
            background-color: #fff;
            min-height: 200px;
            border-radius: 16rpx;
            .order-title {
                font-size: 32rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 32rpx;
                padding-bottom: 20rpx;
            }
            .info-item {
                padding: 20rpx 0;
                line-height: 30rpx;
            }
        }
        .van-cell__value {
            flex: 4;
        }
    }
.order-bottom {
    position: fixed;
    padding-bottom: calc(5px + env(safe-area-inset-bottom));
    left: 0;
    right: 0;
    bottom: 0;
    height: 104rpx;
    z-index: 20;
    background: #ffffff;
    .btn-container {
        padding: 20rpx 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
    }
    .btn {
        height: 64rpx;
        background: #ffffff;
        border-radius: 32rpx;
        border: 2rpx solid #cccccc;
        margin-right: 24rpx;
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 36rpx;
        padding: 14rpx 32rpx;
        box-sizing: border-box;
        border-radius: 32rpx;
    }
    .pay {
        color: #f5001d;
        border-color: #f5001d;
    }
}
