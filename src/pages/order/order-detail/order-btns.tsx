import api from "@/util/api";
import { MiniNavigateTo, MiniRedirectTo } from "@/util/route";
import MiniToast from "@/util/toast";
import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";


export default ({
    handleOption,
    orderNo,
    load,
}) => {
    const getWxPayParams = (orderId) => {
        api.getWxPayParams({
            data: {
                orderId: orderId
            },
            method: 'POST',
        }).then(res => {
            const payObj = res;
            console.log(res)
            Taro.requestPayment({
                //@ts-ignore
                appId: payObj.appId,
                timeStamp: payObj.timeStamp,
                nonceStr: payObj.nonceStr,
                package: payObj.packageValue,
                signType: payObj.signType,
                paySign: payObj.paySign,
                success: (successRes) => {
                    console.log("successRes:", successRes);
                },
                fail: (res) => {
                    MiniToast.error('支付失败');
                }
            })
        }).catch((err) => {
        })
    }

    const cancelOrder = (orderId) => {
        api.cancelOrder({
            data: {
                orderId: orderId
            },
            method: 'POST',
        }).then(res => {
            load();
        })
    }

    const refundOrder = (orderId) => {
        api.refundOrder({
            data: {
                orderId: orderId
            },
            method: 'POST',
        }).then(res => {
            load();
        })
    }

    const confirmOrder = (orderId) => {
        api.confirmOrder({
            data: {
                orderId: orderId
            },
            method: 'POST',
        }).then(res => {
            load();
        })
    }

    const delOrder = (orderId) => {
        api.delOrder({
            data: {
                orderId: orderId
            },
            method: 'POST',
        }).then(res => {
            load();
        })
    }
    return (<>

        {
            handleOption.cancel &&
            <View className="btn" onClick={e => {
                e.stopPropagation();
                MiniToast.showModal({
                    title: '提示',
                    content: '确定取消吗？',
                    success: function (res) {
                        if (res.confirm) {
                            cancelOrder(orderNo);
                        } else if (res.cancel) {
                            console.log('用户点击取消')
                        }
                    }
                })
            }} >
                取消
            </View>
        }
        {handleOption.pay &&
            < View className="btn pay" onClick={e => {
                e.stopPropagation();
                // getWxPayParams(orderNo);
                MiniNavigateTo({
                    url: '/pages/order/cashier/index?orderNo=' + orderNo
                });
            }}>
                去支付
            </View>
        }
        {handleOption.refund && <View className="btn" onClick={e => {
            e.stopPropagation();
            MiniToast.showModal({
                title: '提示',
                content: '确定取消订单吗？',
                success: function (res) {
                    if (res.confirm) {
                        refundOrder(orderNo);
                    } else if (res.cancel) {
                        console.log('用户点击取消')
                    }
                }
            })
        }}>
            取消订单
        </View>
        }
        {
            handleOption.confirm &&
            <View className="btn" onClick={e => {
                e.stopPropagation();
                confirmOrder(orderNo);
            }}>
                确认收货
            </View>
        }
        {
            handleOption.delete && <View className="btn" onClick={e => {
                e.stopPropagation();
                delOrder(orderNo)
            }}>
                删除
            </View>
        }

    </>)
}