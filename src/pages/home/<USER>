/**
 * @description 首页
 * <AUTHOR>
 */
import { MiniReLaunch } from "@/util/route";
import { View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.less";
definePageConfig({
    navigationStyle: "custom",
    navigationBarTitleText: "7 RIVERLIGHT",
});
const Home = () => {
    const [countdown, setCountdown] = useState(3);
    useEffect(() => {
        if (countdown === 0) {
            // 在这里执行页面跳转的逻辑
            console.log(1);
            MiniReLaunch({ url: "/pages/container/index" });
        } else {
            const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
            // MiniRedirectTo({ url: '/pages/container/index' })
            return () => clearTimeout(timer);
        }
    }, [countdown]);

    const handleButtonClick = () => {
        MiniReLaunch({ url: "/pages/container/index" });
    };

    return (
        <View className="container">
            <button onClick={handleButtonClick} className="container-btn">
                跳过{countdown}
            </button>
        </View>
    );
};
export default Home;
