import LimitTimePicker from '@/components/limit-time-picker'
import { View, But<PERSON>, Text } from '@tarojs/components'
// import CodeCreator from "taro-code-creator"
import React from 'react'
// import './index.less'
// definePageConfig({
//     navigationBarTitleText: '首页1'
// })
import { Barcode, QRCode } from 'taro-code'

export default () => {
    return (
        <View className='index'>
            首页
            {/* <LimitTimePicker onChange={(value) => {
                console.log(value)
            }} value={undefined} /> */}
            <QRCode
                text='world'
                size={300}
                scale={4}
                errorCorrectLevel='M'
                typeNumber={2}
            />
        </View>
    )
}