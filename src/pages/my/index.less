.person {
    padding: 24px;

    &-assets-items {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 36px;
    }
    &-asset-item {
        text-align: center;
        padding-right: 20px;
        flex: 1;
        > view:first-child {
            padding-bottom: 24px;
        }
        &.line {
            border-right: 1px solid @primaryColor;
        }
        &:last-child {
            padding-left: 0;
            &.line {
                border-right: 0;
            }
        }
    }
}

.person-info {
    --cell-group-inset-padding: 0px;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100% 400px;
    padding: 0 24px;
    .balance-and-user {
        display: flex;
    }
    .balance-box:nth-child(2) {
        margin-left: 20px;
    }
    .guiderForm_box {
        width: 614px;
        height: 622px;
        border-radius: 16px;
        background: #ffffff;
        box-sizing: border-box;
        .guiderForm_title {
            font-size: 32px;
            font-family: PingFangSC-Medium, PingFang SC;
            line-height: 96px;
            text-align: center;
        }

        .btn_box {
            display: flex;
            margin-top: 60px;
            .from_btn {
                width: 200px;
                height: 80px;
                border-radius: 100px;
                font-size: 28px;
                line-height: 80px;
                color: #fff;
            }
            .submit_btn {
                background: linear-gradient(90deg, #ff3b61 0%, #ff0940 100%);
            }
            .close_btn {
                background: #cccccc;
            }
            .from_btn::after {
                border: none;
            }
        }
    }
}
