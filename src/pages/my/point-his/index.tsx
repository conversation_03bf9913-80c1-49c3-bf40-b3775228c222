import api from "@/util/api";
import { MiniNavigateTo } from "@/util/route";
import { Button } from "@antmjs/vantui";
import { Image, View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.less";
const PointHis = () => {
    const [pointList, setPointList] = useState<any>([]);

    const getPointHisList = () => {
        api.getPointHisList({
            data: {
                size: 999,
            },
            method: "get",
        }).then(res => {
            setPointList(res);
        });
    };

    useEffect(() => {
        getPointHisList();
    }, []);

    return (
        <View
            className="point-his"
            onClick={() =>
                MiniNavigateTo({
                    url: "/pages/point-his/index",
                })
            }>
            <View className="point-his-box">
                {pointList.map(item => (
                    <View className="point-his-item" key={item.id}>
                        <View className="point-his-item-content">
                            <View className="time">{item.yyyyMM}</View>
                            <View className="info">
                                <View>{item.description}</View>
                                <View>{item.amount}</View>
                            </View>
                            <View className="time-detail">{item.createTime}</View>
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};
export default PointHis;
