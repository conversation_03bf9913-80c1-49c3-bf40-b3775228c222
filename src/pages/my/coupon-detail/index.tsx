// getCouponDetail
import React, { useEffect, useState } from 'react';
import { View, Button, Text } from '@tarojs/components';
import './index.less';
import api from '@/util/api';
import { useRouter } from '@tarojs/taro';
import CouponItem from '../coupon/coupon-item';
import { QRCode } from 'taro-code';
import { Icon, Popup } from "@antmjs/vantui"
import Taro from '@tarojs/taro';
const Coupon = () => {
    const route = useRouter()
    const [couponDetail, setCouponDetail] = useState({});
    const [show, setShow] = useState(false)
    const getDetail = () => {
        api.getCouponDetail({
            data: {
                couponUserId: route.params.id
            }
        }).then(res => {
            console.log("res:", res)
            setCouponDetail(res);
            Taro.setNavigationBarTitle(res.name)
        })
    }
    useEffect(() => {
        Taro.setNavigationBarTitle({ title: route.params.name })
        getDetail()
    }, [])
    return (
        <View className='coupon-container'>
            <View className='coupon-head'>
                <CouponItem data={couponDetail}
                    stopLink={true}
                />
                {
                    couponDetail?.couponQrCode && <View className='coupon-code' onClick={() => {
                        setShow(true);
                    }}>
                        <QRCode
                            text={`<${couponDetail?.couponQrCode}>`}
                            size={100}
                            scale={4}
                            errorCorrectLevel='M'
                            typeNumber={2}
                        />
                    </View>
                }

            </View>

            <View className='coupon-intro'>
                <View className='coupon-restrictions'>
                    <View className='restriction-title'>使用限制</View>
                </View>
                <View className='coupon-stores'>
                    <View className='store-title'>可用门店</View>
                    <View className='coupon-text'>限中国内地地区(不含港澳台)指定门店使用</View>
                </View>
                <View className='coupon-time'>
                    <View className='time-title'>可用时段</View>
                    <View className='coupon-text'>周一至周日; 全天可用</View>
                </View>
                <View className='coupon-products'>
                    <View className='product-title'>适用商品</View>
                    <View className='coupon-text'>{couponDetail?.applicableGoods}</View>
                </View>
                <View className='coupon-scenarios'>
                    <View className='scenario-title'>使用场景</View>
                    <View className='coupon-text'>下单的商品在7RiverLight小程序菜单分类中方可使用该券</View>
                    <View className='coupon-text'>在门店下单时，出示“核销码”给店员进行扫码后可用</View>
                </View>
                <View className='coupon-scenarios'>
                    <View className='scenario-title'>使用场说明</View>
                    <View className='coupon-text'>不可与现金券，赠饮券，买赠券，折扣券，换购券同时使用</View>
                    <View className='coupon-text'>运费不参与优惠券使用门槛计算，也不可使用优惠</View>
                    <View className='coupon-text'>现金券仅限单笔订单当次使用，不找零，不兑现，结算</View>

                </View>
            </View>

            {/* <Button className='coupon-use-button'>去使用</Button> */}
            <View />
            <Popup show={show} onClose={() => {
                // clearInterval(timer)
                setShow(false)
            }} round>
                <View style={{
                    width: '300px', height: '400px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '64px'
                }}>
                    <View>
                        {/* 核销码 */}
                        <View style={{ fontSize: '16px', marginBottom: '20px', textAlign: 'center' }}>
                            核销码
                        </View>
                        {
                            couponDetail?.couponQrCode &&
                            <QRCode
                                text={`<${couponDetail?.couponQrCode}>`}
                                size={200}
                                scale={4}
                                errorCorrectLevel='M'
                                typeNumber={2}
                            />
                        }

                    </View>


                </View>
            </Popup >
        </View>
    );
};

export default Coupon;