.coupon-container {
    // padding: 16px;
    padding-bottom:calc(80px + env(safe-area-inset-bottom));
    .coupon-head{
        background-color: #f5f5f5;
        padding: 24px 16px;
    }
    .coupon-intro{
        padding: 16px 20px;
    }
    .coupon-restrictions, .coupon-stores, .coupon-time, .coupon-products, .coupon-scenarios {
      margin-top: 16px;
    }
    .coupon-restrictions{
        font-size: 32px;
        margin-bottom: 20px;
    }
    .coupon-text{
        color: #716b6b;
        margin-top: 10px;
        padding-left: 20px;
        position: relative;
        &::before{
           content: '';
           width: 2px;
           height: 2px;
           background-color: #716b6b;
           position: absolute;
           left: 9px;
           top: 19px;
        }
    }
    .coupon-code{
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border: 10px solid @primaryColor;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        margin: auto;
    }
    .coupon-use-button {
      margin-top: 24px;
      background-color: @primaryColor;
      color: white;
      margin: 30px 30px 130px 30px;
    //   margin-bottom: ;
    }
  }