import { View, Text, Image } from "@tarojs/components"
import { icon_expire } from "@/images/index";
import './coupon-item.less'
import { MiniNavigateTo } from "@/util/route";
export default ({ data, stopLink }) => {
    return (<View className="coupon-item"
        onClick={() => {
            !stopLink && MiniNavigateTo({
                url: "/pages/my/coupon-detail/index?id=" + data.couponUserId + "&name=" + data.name,
            })
        }}
        style={{
            opacity: data.status !== 2 ? 1 : 0.5
        }}>
        <View className="coupon-content">
            <View className="coupon-name">{data.name}</View>
            <View className="coupon-desc">{data.desc}</View>
            {
                data.goodsType === 2 ?
                    <View className="coupon-right">
                        <Text>兑换券</Text>
                    </View>
                    :
                    <View className="coupon-right">
                        <Text>减¥</Text>
                        <Text className="coupon-discount">{data.discount}</Text>
                    </View>

            }

        </View>
        <View className="coupon-bottom">
            <View>有效期：{data.startTime}至{data.endTime}</View>
        </View>
        {
            data.status === 2 &&
            <Image src={icon_expire} className="coupon-expire" />
        }
    </View>)
}