import { View } from "@tarojs/components"
import Header from "./header"
import './index.less'
import { Empty, Button, PullToRefresh, InfiniteScroll } from '@antmjs/vantui'
import { useDidShow, useRouter } from "@tarojs/taro"
import { useEffect, useRef, useState } from "react"
import api from "@/util/api"
import CouponItem from "./coupon-item"
definePageConfig({
    navigationBarTitleText: '优惠券'
})
export default () => {
    const route = useRouter();
    const [data, setData] = useState<any[]>([]);
    const infiniteScrollRef = useRef<any>();
    const pageSize = 20;
    const pageNo = useRef(1);

    const couponType = useRef(0)

    const getOrderDatas = () => {

        return api.getCouponList({
            data: {
                page: pageNo.current || 1,
                size: pageSize,
                status: couponType.current, // 订单类型，1为正常订单，3为积分订单
                // order: todayOrHistory.current,
            },
            method: 'get',
            showError: false,
            showLoad: false,
            filterCheck: true,
        });
    };
    const onRefresh = () => {
        pageNo.current = 1;
        return new Promise(async (resolve: (value: undefined) => void) => {
            infiniteScrollRef.current?.reset();
            try {
                const result = await getOrderDatas();
                pageNo.current++;
                setData(result.couponUserList);
            } catch (error) {
            }
            infiniteScrollRef.current?.reset(true);
            resolve(undefined);
        });
    };
    const loadMore = () => {
        return new Promise(async (resolve: (value: "loading" | "error" | "complete") => void) => {
            try {
                const result = await getOrderDatas();
                let newData: any[] = [];
                if (pageNo.current === 1) {
                    newData = result.couponUserList;
                } else {
                    newData = data.concat(result.couponUserList);
                }
                pageNo.current++;
                setData(newData);
                //  endStatus.current = newData.length >= result.total;
                resolve(newData.length >= result.count ? "complete" : "loading");
            } catch (error) {
                resolve("error");
            }
        });
    };

    useDidShow(() => {
        pageNo.current = 1;
        infiniteScrollRef.current?.reset(true);
    });
    return (<View className="order">
        <Header onChange={(data) => {
            couponType.current = data.orderType
            setData([])
            pageNo.current = 1;
            infiniteScrollRef.current?.reset(true);
        }} />
        <PullToRefresh onRefresh={onRefresh} style={{ backgroundColor: '#f1f2f3' }}>
            <View className="order-list">
                {data.map((item) => <CouponItem data={item} key={item.id} load={() => {
                    pageNo.current = 1;
                    infiniteScrollRef.current?.reset(true);
                }} />)}
            </View>
            <InfiniteScroll
                completeText={
                    <>
                        {data.length == 0 ? (
                            <Empty description="暂无优惠券" >
                            </Empty>
                        ) : (
                            "没有更多了"
                        )}
                    </>
                }
                loadMore={loadMore}
                ref={infiniteScrollRef}
            />
        </PullToRefresh>
    </View>)
}