import { View } from "@tarojs/components"
import { useState } from "react";
import './header.less';
export default ({ onChange }) => {
    const [isCurrent, setCurrent] = useState(true);
    const [orderType, setOrderType] = useState(0);
    const list = [
        {
            label: '未使用',
            type: 0
        },
        {
            label: '已使用',
            type: 1
        },
        {
            label: '已作废',
            type: 2
        },
    ]
    return (<View className="order-header">
        <View className="type-tab">
            {
                list.map((item) => {
                    return (<View key={item.label} className="type-tab-item">
                        <View className={item.type === orderType ? 'active' : ''}
                            onClick={() => {
                                setOrderType(item.type);
                                onChange({ timeType: isCurrent ? 1 : 0, orderType: item.type })
                            }}>
                            {item.label}
                        </View>
                    </View>)
                })
            }
        </View>
    </View>)
}