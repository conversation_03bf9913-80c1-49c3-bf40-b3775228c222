import { View } from "@tarojs/components";
import { DDYNavigateTo } from "@/utils/route";

export default function UserList({ newIdentity }) {
    const isServiceProvider = newIdentity === "serviceProvider";
    return (
        <View
            className="balance-box"
            onClick={() => DDYNavigateTo({ url: `/pages/${isServiceProvider ? "store-list" : "guider-list"}/index` })}
        >
            <View className="balance-left">
                <View className="balance-title">{isServiceProvider ? "门店" : "导购"}列表</View>
                <View className="balance-detail">查看{isServiceProvider ? "门店" : "导购"}信息</View>
            </View>
            <View className="balance-right">去添加</View>
        </View>
    );
}
