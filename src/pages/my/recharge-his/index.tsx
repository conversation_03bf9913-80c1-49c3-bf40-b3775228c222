import api from "@/util/api";
import { MiniNavigateTo } from "@/util/route";
import { Button } from "@antmjs/vantui";
import { Image, View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.less";
definePageConfig({
    navigationBarTitleText: "充值记录"
})
const RechargeHis = () => {
    const [rechargeList, setRechargeList] = useState<any>([]);

    const getRechargeHisList = () => {
        api.getRechargeHisList({
            data: {
                pageIgnore: 1,
            },
            method: "post",
        }).then(res => {
            setRechargeList(res);
        });
    };

    useEffect(() => {
        getRechargeHisList();
    }, []);

    return (
        <View className="recharge-his">
            <View className="recharge-his-box">
                {rechargeList.map(item => (
                    <View className="recharge-his-item" key={item.id}>
                        <View className="recharge-his-item-content">
                            <View className="info">
                                <View>充值:{item.rechargeAmount}元</View>
                            </View>
                            <View className="info">
                                <View>赠送余额:{item.giveAmount == null ? 0 : item.giveAmount}</View>
                                <View>赠送积分:{item.givePoint == null ? 0 : item.givePoint}</View>
                            </View>
                            <View className="time-detail">支付时间:{item.payTime}</View>
                        </View>
                    </View>
                ))}
            </View>
        </View>
    );
};
export default RechargeHis;
