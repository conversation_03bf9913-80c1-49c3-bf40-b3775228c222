import { View } from "@tarojs/components";
import Header from "./header";
import MenuList from "./menu-list";
import VipMe from "./vip-me";
import "./index.less";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { MiniNavigateTo } from "@/util/route";
import api from "@/util/api";
import userStore from "@/store/user";
import { loginCheck } from "@/util/ylogin";

const Info = React.forwardRef((props, ref) => {
    const [user, setUser] = useState({})
    let types = [
        { label: "积分", name: "points", path: '/pages/my/point/index' },
        { label: "余额", name: "balance", path: "/pages/my/balance/index" },
        { label: "优惠券", name: "couponCount", path: '/pages/my/coupon/index' },
    ];

    const getUserDetail = () => {
        api.getUserDetail({
            showError: false
        }).then(res => {
            setUser(res);
            loginCheck().then(res => {
                console.log(res)
            })
        })
    }

    useEffect(() => {
        getUserDetail()
    }, [])

    useImperativeHandle(ref, () => ({
        load: getUserDetail,
    }));
    return (
        <View className="person-info">
            <Header />
            <View className="person-assets-items">
                {types.map((item, index) => {
                    return (
                        <View key={index} className="person-asset-item line"
                            onClick={() => {
                                MiniNavigateTo({ url: item.path })
                            }}
                        >
                            <View>{item.label}</View>
                            <View>{user[item.name] || 0}</View>
                        </View>
                    );
                })}
            </View>
            <VipMe user={user} />
            <MenuList onTap={props.onTap} />
        </View>
    );
});

export default Info;
