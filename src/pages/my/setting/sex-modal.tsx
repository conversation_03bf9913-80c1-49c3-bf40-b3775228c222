import { ActionSheet, Picker } from "@antmjs/vantui";

let columns = ["未知", "男", "女"];
export default ({ show, onClose, valueKey }) => {
    const onChange = event => {
        const { value, index } = event.detail;
        console.log(value)
        onClose(index || 0);
    };
    return (
        <ActionSheet show={show} onClose={() => onClose()}>
            <Picker
                columns={columns}
                valueKey={valueKey || 0}
                onConfirm={onChange}
                onCancel={() => onClose()}
            />
        </ActionSheet>
    );
};

