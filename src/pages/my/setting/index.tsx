import api from "@/util/api";
import { <PERSON><PERSON><PERSON>, MiniNavigateTo } from "@/util/route";
import { Button, Cell, Field, Row } from "@antmjs/vantui";
import { View, Image, Text, OpenData } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useEffect, useState } from "react";
import BirthdayModal from "./birthday-modal";
import SexModal from "./sex-modal";
import MiniToast from "@/util/toast";
import './index.less'
import UseIcon from '../../../images/login.png';
let columns = ["未知", "男", "女"];
definePageConfig({
    backgroundTextStyle: "light",
    navigationBarTitleText: "账户设置",
});

export default () => {
    const [useInfo, setUseInfo] = useState({
        nickName: "",
        gender: 0,
        birthday: "",
        telephone: "",
        avatar: "",
        userName: "",
        joinMemberDateDesc: ""
    });

    const [sexShow, setSexShow] = useState(false);
    const [birthdayShow, setBirthdayShow] = useState(false);

    const getUserDetail = () => {
        api.getNewUserInfo({
            // method: "POST",
            showError: false
        }).then(res => {
            setUseInfo({
                avatar: res.avatar,
                nickName: res.nickName,
                userName: res.userName,
                gender: res.gender,
                birthday: res.birthday,
                telephone: res.telephone,
                joinMemberDateDesc: res.joinMemberDateDesc
            });
        })
    }

    const submitInfo = () => {
        api.updateUserInfo({
            method: "POST",
            data: {
                ...useInfo,
            },
            filterCheck: true,
        }).then(res => {
            MiniToast.success("保存成功");
            setTimeout(() => {
                MiniBack();
            }, 1000);
        });
    };

    useEffect(() => {
        getUserDetail();
    }, []);

    return (
        <View className="setting">
            <View className="header">
                <Button className="btn-avatar" onClick={(e) => {
                    // setUseInfo({ ...useInfo, avatar: e.detail.avatarUrl });
                    Taro.chooseImage({
                        success(result) {
                            var tempFilePaths = result.tempFilePaths;
                            MiniToast.showLoading("图片上传中");
                            Taro.uploadFile({
                                url: `https://www.7riverlight.com/wx/storage/upload`, //仅为示例，非真实的接口地址
                                filePath: tempFilePaths[0],
                                name: "file",
                                success: function (res) {
                                    console.log(res.data);
                                    MiniToast.hideLoading();
                                    try {
                                        const json = JSON.parse(res.data);
                                        console.log(json)
                                        setUseInfo({ ...useInfo, avatar: json.data.url });
                                    } catch (error) { }
                                },
                                fail: function () {
                                    MiniToast.hideLoading();
                                },
                            });
                        },
                    });
                }}>
                    <Image src={useInfo?.avatar || UseIcon} className="avatar" />
                </Button>
                <Text className="header-other">{useInfo?.joinMemberDateDesc}</Text>
            </View>
            <Cell
                title="昵称"
                renderExtra={
                    <Field
                        value={useInfo?.nickName}
                        style={"width: 400rpx;text-align:right;"}
                        border={false}
                        placeholder="请输入昵称"
                        //@ts-ignore
                        type="nickname"
                        onChange={event => {
                            console.log("event.detail:", event.detail)
                            setUseInfo({ ...useInfo, nickName: event.detail });
                        }}
                    />
                }
            />
            <Cell
                title="姓名"
                renderExtra={
                    <Field
                        value={useInfo?.userName}
                        style={"width: 400rpx;text-align:right;"}
                        border={false}
                        placeholder="请输入姓名"
                        onChange={event => {
                            console.log(event)
                            setUseInfo({ ...useInfo, userName: event.detail });
                        }}
                    />
                }
            />
            <Cell
                title="性别"
                isLink
                value={columns[useInfo?.gender || 0]}
                onClick={() => {
                    setSexShow(true);
                }}
            />
            <Cell
                title="手机"
                renderExtra={
                    <Field
                        value={useInfo.telephone}
                        style={"width: 400rpx;text-align:right;"}
                        border={false}
                        placeholder="请输入手机"
                        onChange={event => {
                            setUseInfo({ ...useInfo, telephone: event.detail });
                        }}
                    />
                }
            />
            <Cell
                title="生日"
                isLink
                value={useInfo.birthday}
                onClick={() => {
                    setBirthdayShow(true);
                }}
            />
            <View className="bottom-btn">
                <Button
                    block
                    style={{ margin: "40rpx 24rpx", width: "702rpx", backgroundColor: ' #4b5c10', color: '#fff' }}
                    round
                    onClick={() => {
                        submitInfo();
                    }}
                >
                    保存
                </Button>
            </View>

            {/** 弹框区域 */}

            {/** 性别选择弹框 */}
            <SexModal
                valueKey={useInfo.gender}
                show={sexShow}
                onClose={index => {
                    console.log(index);
                    setSexShow(false);
                    if (index !== undefined) {
                        setUseInfo({ ...useInfo, gender: index });
                    }
                }}
            />

            {/** 生日选择弹框 */}
            <BirthdayModal
                valueKey={useInfo.birthday}
                show={birthdayShow}
                onClose={val => {
                    setBirthdayShow(false);
                    if (val) {
                        setUseInfo({ ...useInfo, birthday: val });
                    }
                }}
            />
        </View>
    );
};
