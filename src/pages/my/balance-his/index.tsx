import api from "@/util/api";
import { MiniNavigateTo } from "@/util/route";
import { Button, Empty } from "@antmjs/vantui";
import { Image, View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.less";
definePageConfig({
    navigationBarTitleText: '消费记录'
})
const BalanceHis = () => {
    const [balanceList, setBalancetList] = useState<any>([]);

    const getBalanceHisList = () => {
        api.getBalanceHisList({
            data: {
                pageIgnore: 1,
            },
            method: "post",
        }).then(res => {
            setBalancetList(res);
        });
    };

    useEffect(() => {
        getBalanceHisList();
    }, []);

    return (
        <View className="balance-his">
            <View className="balance-his-box">
                {balanceList.map(item => (
                    <View className="balance-his-item" key={item.id}>
                        <View className="balance-his-item-content">
                            <View className="info">
                                <View>订单:{item.orderSn}{item.amount > 0 ? "新增" : '消费'}{item.amount}元</View>
                            </View>
                            <View className="info">
                                <View>剩余金额:{item.leftBalance}元</View>
                            </View>
                            <View className="time-detail">{item.addTime}</View>
                        </View>
                    </View>
                ))}
                {
                    balanceList.length === 0 && <Empty description="还没有消费记录哦～" />
                }
            </View>
        </View>
    );
};
export default BalanceHis;
