import { Button, Image } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { pxTransform } from "@tarojs/taro";
import "./index.less";
import MiniToast from "@/util/toast";
import { loginCheck } from "@/util/ylogin";
import userStore from "@/store/user";
import { observer } from "mobx-react";
import { MiniNavigateTo } from "@/util/route";

const Header = observer(() => {
    const onGetUserInfo = (e) => {
        MiniToast.showLoading("登录中");
        MiniNavigateTo({ url: '/pages/login/index' })
    };
    const user = userStore.getUser();
    return (
        <View className="person-header">
            <Image width={pxTransform(128)} height={pxTransform(128)} className="person-header-avatar" src={user.avatarUrl || "https://7riverlight.oss-cn-hangzhou.aliyuncs.com/mini-shop/avatar.jpg"} />
            <View className="person-header-info">
                <View>{user.nickName || 'Hello'}</View>
                <View>欢迎加入7 RIVERLIGHT的会员</View>
            </View>
            {!user.userId &&
                <Button type="primary" className="primary-btn" openType='getUserInfo' onGetUserInfo={onGetUserInfo} size="small">
                    快速注册
                </Button>
            }
        </View>
    );
});
export default Header;
