.person-header {
    display: flex;
    align-items: center;
    padding-top: 40px;
    &-avatar {
        image {
            width: 128px;
            height: 128px;
            border-radius: 16px;
        }
    }
    &-info {
        display: flex;
        flex-direction: column;
        margin-left: 24px;
        > view:nth-child(2) {
            color: @primaryColor;
            font-size: 24px;
        }
    }
}
