import api from "@/util/api";
import { MiniNavigateTo } from "@/util/route";
import MiniToast from "@/util/toast";
import { Button, Icon } from "@antmjs/vantui";
import { Image, View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.less";

definePageConfig({
    navigationBarTitleText: '我的积分'
})
const Point = () => {
    const [point, setPoint] = useState(0);
    const [exchangeList, setExchangeList] = useState<any>([]);

    const getExchangeList = () => {
        api.getPointExchange({ method: "get" }).then(res => {
            setExchangeList(res);
        });
    };

    const getUserPoint = () => {
        api.getUserDetail({
            method: "get",
        }).then(res => {
            setPoint(res.points);
        });
    };

    const handleExchangeClick = id => {
        api.exchangeCoupons({
            data: {
                id: id,
                num: 1,
            },
            method: "post",
        }).then(res => {
            MiniToast.success("兑换成功");
            getUserPoint();
        });
    };

    useEffect(() => {
        getExchangeList();
        getUserPoint();
    }, []);

    return (
        <View className="point">
            <View className="point-title">我的积分</View>
            <View className="point-num"> {point} </View>
            <View
                className="point-his"
                onClick={() => {
                    MiniNavigateTo({
                        url: `/pages/my/point-his/index`,
                    });
                }}>
                <Icon name="underway-o" size="14px"></Icon> 历史积分
            </View>
            <View className="point-box">
                {exchangeList.map(item => (
                    <View className="point-box-item" key={item.id}>
                        <Image src={item.couponPic} />
                        <View className="name">{item.couponName}</View>
                        <View className="num">{item.amount} 积分</View>
                        <Button
                            className={`button ${item.canExchanged ? "active" : ""}`}
                            onClick={() => handleExchangeClick(item.id)}>
                            {item.canExchanged ? "兑换" : "积分不足"}
                        </Button>
                        <View className="line"></View>
                    </View>
                ))}
            </View>
        </View>
    );
};
export default Point;
