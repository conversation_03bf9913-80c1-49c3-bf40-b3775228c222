import api from "@/util/api";
import { MiniNavigateTo } from "@/util/route";
import { Button, Icon } from "@antmjs/vantui";
import { Image, View } from "@tarojs/components";
import { useEffect, useState } from "react";
import "./index.less";
import MiniToast from "@/util/toast";
import Taro from "@tarojs/taro";
definePageConfig({
    navigationBarTitleText: "我的余额",
});
const Point = () => {
    const [balance, setBalance] = useState(0);
    const [user, setUser] = useState([]);
    const [rechargeConfigList, setRechargeConfigList] = useState<any>([]);

    const getRechangeConfigList = () => {
        api.getRechargeConfig({
            data: {
                pageIgnore: 1,
            },
            method: "post",
        }).then(res => {
            setRechargeConfigList(res);
        });
    };

    const getUserBalance = () => {
        api.getUserDetail({
            method: "get",
        }).then(res => {
            setBalance(res.balance);
            setUser(res);
        });
    };

    const doRecharge = (configId, amount) => {
        api.preRecharge({
            method: "post",
            data: {
                configId,
                rechargeAmount: amount,
            },
        }).then(res => {
            const payObj = res;
            Taro.requestPayment({
                //@ts-ignore
                appId: payObj.appId,
                timeStamp: payObj.timeStamp,
                nonceStr: payObj.nonceStr,
                package: payObj.packageValue,
                signType: payObj.signType,
                paySign: payObj.paySign,
                success: successRes => {
                    MiniToast.success("支付成功");
                    getUserBalance();
                },
                fail(err) {
                    console.log(err);
                    MiniToast.error("支付失败");
                },
            });
        });
    };

    useEffect(() => {
        getRechangeConfigList();
        getUserBalance();
    }, []);

    return (
        <View className="balance">
            <View>
                <View className="balance-title">我的余额</View>
                <View className="balance-num"> {balance || 0.0} </View>
                <View className="balance-navi">
                    <View
                        className="balance-his"
                        onClick={() => {
                            MiniNavigateTo({
                                url: `/pages/my/recharge-his/index`,
                            });
                        }}>
                        充值记录
                    </View>
                    <View
                        className="balance-his"
                        onClick={() => {
                            MiniNavigateTo({
                                url: `/pages/my/balance-his/index`,
                            });
                        }}>
                        消费记录
                    </View>
                </View>
            </View>

            <View className="balance-box">
                {rechargeConfigList.map(item => (
                    <View className="balance-box-item" key={item.id} onClick={() => doRecharge(item.id, item.amount)}>
                        <Image src={item.picurl} />
                        <View className="num">{item.amount}元储蓄卡</View>
                        <Button className="button">充值</Button>
                    </View>
                ))}
            </View>
        </View>
    );
};
export default Point;
