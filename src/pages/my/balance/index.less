.balance {
    background: #ffffff;
    margin: 32px;
    height: 100vh;
    padding-bottom: env(safe-area-inset-bottom);
    &-num {
        margin: 24px 0 32px;
        font-family: "D-DIN";
        font-size: 48px;
        font-weight: bold;
        color: @primaryColor;
    }

    &-navi {
        display: flex;
        justify-content: flex-end;
        > view:first-child {
            padding-right: 24px;
            border-right: 1px solid #cecdcd;
        }
        > view:last-child {
            padding-left: 24px;
        }
    }

    &-his {
        font-size: 24px;
        color: #999;
        margin-bottom: 24px;
        text-align: right;
    }

    &-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        &-item {
            width: 50%;
            padding-right: 24px;
            box-sizing: border-box;
            margin-bottom: 36px;
            image {
                height: 300px;
                width: 100%;
                border-radius: 40px;
            }
            &:nth-child(2n) {
                padding-left: 24px;
                padding-right: 0;
            }
            .name {
                font-size: 28px;
                margin-top: 16px;
            }
            .num {
                font-weight: bold;
                margin-top: 16px;
            }
            .button {
                color: #fff;
                border-radius: 50px;
                margin-top: 16px;
                height: 60px;
                background: #4b5c10;
            }
        }
    }
 
}
