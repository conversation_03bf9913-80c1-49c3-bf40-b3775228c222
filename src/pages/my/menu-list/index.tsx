import { View, Text } from "@tarojs/components";
import { Button, Cell, CellGroup, CellProps, Grid, GridItem, Icon } from "@antmjs/vantui";
import { useMemo } from "react";
import "./index.less";
import { MiniNavigateTo } from "@/util/route";

export default function MenuList({ onTap }) {
    const buyerMenuList = useMemo(() => {
        return [
            {
                title: "订单中心",
                url: "/pages/order/order/index",
            },
            {
                title: "积分兑换",
                url: "/pages/my/point/index",
            },
            { title: "设置", name: "setting", url: '/pages/my/setting/index' },

        ];
    }, []);
    return (
        <View className="menu-list">
            <CellGroup inset>
                {/*消费者菜单*/}
                {buyerMenuList.map((item: any) => {
                    return (
                        // eslint-disable-next-line react/jsx-key
                        <Cell
                            isLink
                            renderIcon={<Icon name={`${item.icon} text-icon-list`} classPrefix={"iconfont"} />}
                            title={item.title}
                            onClick={() => {
                                if (item.url === '/pages/order/order/index') {
                                    return onTap()
                                }
                                MiniNavigateTo({
                                    url: item.url
                                })
                            }}
                        />
                    );
                })}
            </CellGroup>
        </View>
    );
}
