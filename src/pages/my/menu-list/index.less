.menu-list {
    
    margin-top: 20px;
    .van-cell-group--inset {
        margin: 0;
    }

    .custome-cell-title {
        display: flex;

        .open-button {
            position: absolute;
            height: 89px;
            width: 90%;
            opacity: 0;
        }
    }
    .info-menu {
        position: relative;
        .open-button {
            position: absolute;
            height: 100%;
            width: 100%;
            opacity: 0;
            top: 0;
            right: 0;
        }
    }
    .text-icon {
        color: #f51240;
        font-size: 48px;
        display: inline-block;
        margin-top: 16px;
    }
    .text-icon-list {
        font-size: 32px;
        color: rgb(203, 204, 205);
        margin-right: 15px;
        display: inline-block;
    }
    .van-cell {
        padding: 40px 20px;
    }
    .van-cell::after {
        border-bottom-color: #4b5c10;
    }
    .van-cell:last-child::after{
        border-bottom: none;
        background-color: #f51240;
    }

}
