.product-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 40px;
    .product-img {
        width: 160px;
        height: 160px;
        border-radius: 24px;
        margin-right: 16px;
        position: relative;
        .product-sell-empty{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.4);
            text-align: center;
            line-height: 40px;
            height: 40px;
            font-size: 20px;
            color: #ccc;
        }
    }
    
    .product-info {
        flex: 1;
        word-wrap: break-word;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        height: 160px;
        // padding-right: 24px;
        > view:first-child {
            font-weight: 400;
        }
        &-content,
        &-quantity {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        &-spe{
            font-size: 20px;
            width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 40px;
            line-height: 40px;
            margin-left: 10px;
            white-space: pre;
            color: #999;
        }
        &-btn {
            // width: 30px;
            // height: 30px;
            // line-height: 30px;
            // border-radius: 50%;
            // border: 1px solid #4b5c10;
            // text-align: center;
            margin: 0 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            // background-color: #f1f1f1;
            border: 1px solid #4b5c10;
            color: #444;
            font-size: 24px;

        }
        &-btn1 {
            // margin: 0 12px;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            text-align: center;
            width: 80px;
            height: 40px;
            line-height: 44px;
            // border-radius: 50%;
            // background-color: #f1f1f1;
            // border: 1px solid #4b5c10;
            background-color: @primaryColor;
            color: #fff;
            font-size: 24px;
            position: relative;
          
        }  
        .product-info-count{
            z-index: 10px;
            position: absolute;
            top: -15px;
            /* bottom: 20rpx; */
            right: -9px;
            border: 2px solid #4b5c10;
            border-radius: 50%;
            background-color: #e2c2bb;
            color: #333;
            min-width: 30px;
            text-align: center;
            height: 34px;
            font-size: 20px;
            line-height: 38px;
        }
    }
}
.check-all{
    border-bottom: 1px solid #f5f5f5;
    padding: 20px 24px;
    .shop-name{
        color: #666;
        margin-left: 80px;
        margin-top: 16px;
    }
}
// --submit-bar-background-color:#ccc;
.checked-rect{
    display: flex;
    flex-direction: row;
    padding-left: 20px;
    .checked-left{
        padding: 10px 20px;
    }
}

    .popup-product-item {
        padding: 0 24px 24px 0;
        flex: 1;
        &:last-child {
            margin-bottom: 20px;
        }
        .product-img {
            border-radius: 50%;
            width: 100px;
            height: 100px;
        }
        .product-info {
            height: 100px;
            > view:first-child {
                font-weight: normal;
            }
            &-content {
                > view:first-child {
                    font-weight: 500;
                }
            }
        }
    }
// .van-native-button{
//     background-color: @primaryColor;
// }
.van-button__text{
    color: #fff;
}
.page-bottom{
    position: fixed;
    bottom: 0;
    display: flex;
    left: 0;
    right: 0;
    padding: 20px 24px calc(40px + env(safe-area-inset-bottom)) 24px ;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    justify-content: space-between;
    .order-bottom{
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 16px 30px;
        color: #fff;
        border-radius: 50px;
        background-color:@primaryColor;
        bottom:  calc(30px + env(safe-area-inset-bottom));
        .order-price{
            font-size: 28px;
            margin-right: 20px;
        }
        .order-btn{
            font-size: 24px;
        }
    }
}
.mail-page{
    .safa-rect{
        height: calc(120px + env(safe-area-inset-bottom));
    }
}