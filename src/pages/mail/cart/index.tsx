import { View, But<PERSON>, Text, ScrollView, Image } from '@tarojs/components'
// import CodeCreator from "taro-code-creator"
import React, { Fragment, useEffect, useState } from 'react'
import { Field, NavBar, Icon, Search, Empty, Checkbox, SubmitBar } from '@antmjs/vantui'
import { MiniBack, MiniNavigateTo } from '@/util/route'
import './index.less'
import api from '@/util/api'
import MiniToast from '@/util/toast'
definePageConfig({
    // navigationStyle: 'custom',
    navigationBarTitleText: '购物车'
})

export default () => {
    const [data, setData] = useState([]);
    const [errorOpen, setErrorOpen] = useState(false);
    const [errorList, setErrorList] = useState<string[]>([]);
    const [totalPrice, setTotalPrice] = useState(0.00)
    const [totalQuantity, setTotalQuantity] = useState(0);
    const [other, setOther] = useState({})
    const [note, setNote] = useState("")
    const [count, setCount] = useState(0)

    const [coldChainCart, setColdChainCart] = useState();
    const [commonCart, setCommonCart] = useState();

    const [coldAllChecked, setColdAllChecked] = useState(true);
    const [commonChecked, setCommonChecked] = useState(true);
    const [allChecked, setAllChecked] = useState()

    const getCarts = () => {
        api.getShopCarts({
            method: 'get',
            data: {
                // freightType: 2,
                businessType: 2
            }
        }).then(res => {
            // if(coldChainCart.cartIdList)

            setColdChainCart(res.coldChainCart);
            setCommonCart(res.commonCart)
            // setCommonChecked
            // if()
            let bol = true;
            let num = 0
            let bol1 = true;
            res?.coldChainCart?.cartList?.map((item) => {
                if (!item.checked) {
                    bol = false
                    bol1 = false
                } else {
                    num += item.number
                }

            })
            setColdAllChecked(bol1);
            let bol2 = true;
            res?.commonCart?.cartList?.map((item) => {
                if (!item.checked) {
                    bol = false
                    bol2 = false
                } else {
                    num += item.number
                }

            })
            setCommonChecked(bol2);
            console.log("num:", num)
            setAllChecked(bol);
            setCount(num)
            let price = 0;
            if (res.coldChainCart) {
                price += res.coldChainCart.cartTotal.goodsAmount
            }
            if (res.commonCart) {
                price += res.commonCart.cartTotal.goodsAmount
            }
            console.log("price:", price)
            setTotalPrice(price)
            let arr = [];
            if (res.coldChainCart && res.coldChainCart.errorList.length) {
                arr = [...arr, ...res.coldChainCart.errorList];
            }
            if (res.commonCart && res.commonCart.errorList.length) {
                arr = [...arr, ...res.commonCart.errorList];
            }

            if (arr.length > 0) {
                setErrorList(arr);
                setErrorOpen(true);
            }


        })
    }

    const handleIncrease = (productId, goodsItem) => {
        const number = goodsItem.number ? goodsItem.number + 1 : 1
        api.cartUpdate({
            data: {
                goodsId: goodsItem.goodsId,
                number: number,
                id: goodsItem.id,
                businessType: 2
            },
            method: 'post',
        }).then(res => {
            // updatebase(goodsItem.goodsId, number)
            getCarts();
        })
    };

    // 删除商品
    const handleDecrease = (productId, goodsItem, idKey) => {
        const number = idKey === 'goodsId' ? (goodsItem.number > 0 ? goodsItem.number - 1 : 0) : (goodsItem.checkedCount > 0 ? goodsItem.checkedCount - 1 : 0);
        if (idKey === 'goodsId') {
            api.cartUpdate({
                data: {
                    goodsId: goodsItem.goodsId,
                    number: number,
                    id: goodsItem.id,
                    businessType: 2
                },
                method: 'post',
            }).then(res => {
                getCarts();
            })
        } else {
            api.goodsUpdate({
                data: {
                    number: number,
                    goodsId: goodsItem.id,
                    businessType: 2
                },
                method: 'post',
            }).then(res => {
                // updatebase(goodsItem.id, number)
                getCarts();
            })
        }
    };

    const goLink = () => {
        const commonList = []
        commonCart?.cartList?.map((item) => {
            if (item.checked) {
                commonList.push(item.id);
            }
        })

        const coldList = [];
        coldChainCart?.cartList?.map((item) => {
            if (item.checked) {
                coldList.push(item.id);
            }
        })
        if (!commonList.length && !coldList.length) {
            return MiniToast.info("请选择商品")
        }
        // https://8101-240e-391-cde-d530-5420-25a7-711f-4b31.ngrok-free.app
        MiniNavigateTo({
            url: `/pages/mail/confirm-order/index?commonList=${JSON.stringify(commonList)}&coldList=${JSON.stringify(coldList)}`
        })
    }

    useEffect(() => {
        getCarts();
    }, [])
    return (<View className='mail-page'>
        <ScrollView
            scroll-y
            scroll-with-animation
        >
            {
                coldChainCart && <View>
                    <View className='check-all'>
                        <Checkbox value={coldAllChecked}
                            onChange={e => {
                                setColdAllChecked(e.detail)
                                coldChainCart.cartList?.map(item => {
                                    item.checked = e.detail;
                                })
                                setColdChainCart({
                                    ...coldChainCart,
                                    cartList: [...coldChainCart.cartList]
                                })
                                if (e.detail) {
                                    setAllChecked(commonChecked)
                                } else {
                                    setAllChecked(false);
                                }

                            }}
                            style={{ marginTop: '10px', paddingLeft: '12px' }}
                        >
                            <Text className="shop-title">冷链运输</Text>
                        </Checkbox>
                        {
                            coldChainCart.note &&
                            <Text className='shop-name'>{coldChainCart.note}</Text>
                        }
                    </View>
                    <View style={{ paddingTop: '20px' }}>
                        {coldChainCart.cartList?.map((item: any, index) => (
                            <View className='checked-rect'>
                                <View className='checked-left'>
                                    <Checkbox value={item.checked}
                                        onChange={e => {
                                            coldChainCart.cartList[index].checked = e.detail
                                            let all = coldChainCart.cartList.filter(item => item.checked).length === coldChainCart.cartList.length;
                                            setColdAllChecked(all);
                                            setColdChainCart({
                                                ...coldChainCart,
                                                cartList: [...coldChainCart.cartList]
                                            })
                                            if (!all) {
                                                setAllChecked(false);
                                            } else {
                                                if (commonCart) {
                                                    setAllChecked(commonChecked)
                                                } else {
                                                    setAllChecked(true);
                                                }

                                            }
                                        }}
                                    >
                                    </Checkbox>
                                </View>
                                <View className="product-item popup-product-item" key={item.id}>
                                    <Image src={item.picUrl} className="product-img" />
                                    <View className="product-info">
                                        <View>{item.goodsName}</View>
                                        <View className="product-info-content">
                                            <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                                                {
                                                    item.discountFlag ?
                                                        <View style={{ display: 'flex', flexDirection: 'row' }}>
                                                            <View style={{ textDecoration: 'line-through' }}>￥{item.price}</View>
                                                            <View style={{ marginLeft: '8px', color: 'red' }}>¥{item.discountPrice}</View>
                                                        </View>
                                                        :
                                                        <View>￥{item.price}</View>

                                                }
                                                <View className="product-info-spe">{item.specifications}</View>
                                            </View>

                                            <View className="product-info-quantity">
                                                <View onClick={() => handleDecrease(item.id, item, 'goodsId')} className="product-info-btn">
                                                    -
                                                </View>
                                                <View>{item.number}</View>
                                                <View onClick={() => handleIncrease(item.id, item)} className="product-info-btn">
                                                    +
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </View>

                        ))}
                    </View>
                </View>
            }
            {
                commonCart && <View>
                    <View className='check-all'>
                        <Checkbox value={commonChecked}
                            onChange={e => {
                                setCommonChecked(e.detail);
                                console.log("commonCart:", commonCart)
                                commonCart.cartList?.map(item => {
                                    item.checked = e.detail;
                                })
                                setCommonCart({
                                    ...commonCart,
                                    cartList: [...commonCart.cartList]
                                })
                                if (e.detail) {
                                    setAllChecked(coldAllChecked)
                                } else {
                                    setAllChecked(false);
                                }
                            }}
                            style={{ marginTop: '10px', paddingLeft: '12px' }}
                        >
                            <Text className="shop-title">普快</Text>
                        </Checkbox>
                        {
                            commonCart.note &&
                            <Text className='shop-name'>{commonCart.note}</Text>
                        }
                    </View>
                    <View style={{ paddingTop: '20px' }}>
                        {commonCart.cartList?.map((item: any, index) => (
                            <View className='checked-rect'>
                                <View className='checked-left'>
                                    <Checkbox value={item.checked}
                                        onChange={e => {
                                            commonCart.cartList[index].checked = e.detail
                                            console.log(commonCart)
                                            let all = commonCart.cartList.filter(item => item.checked).length === commonCart.cartList.length;
                                            setCommonChecked(all);
                                            if (!all) {
                                                setAllChecked(false);
                                            } else {
                                                if (coldChainCart) {
                                                    setAllChecked(commonChecked)
                                                } else {
                                                    setAllChecked(true);
                                                }
                                            }
                                            setCommonCart({
                                                ...commonCart,
                                                cartList: [...commonCart.cartList]
                                            })

                                        }}
                                    >
                                    </Checkbox>
                                </View>
                                <View className="product-item popup-product-item" key={item.id}>
                                    <Image src={item.picUrl} className="product-img" />
                                    <View className="product-info">
                                        <View>{item.goodsName}</View>
                                        <View className="product-info-content">
                                            <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                                                {
                                                    item.discountFlag ?
                                                        <View style={{ display: 'flex', flexDirection: 'row' }}>
                                                            <View style={{ textDecoration: 'line-through' }}>￥{item.price}</View>
                                                            <View style={{ marginLeft: '8px', color: 'red' }}>¥{item.discountPrice}</View>
                                                        </View>
                                                        :
                                                        <View>￥{item.price}</View>

                                                }
                                                <View className="product-info-spe">{item.specifications}</View>
                                            </View>

                                            <View className="product-info-quantity">
                                                <View onClick={() => handleDecrease(item.id, item, 'goodsId')} className="product-info-btn">
                                                    -
                                                </View>
                                                <View>{item.number}</View>
                                                <View onClick={() => handleIncrease(item.id, item)} className="product-info-btn">
                                                    +
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </View>

                        ))}
                    </View>
                </View>
            }
            {
                !coldChainCart && !commonCart ?
                    <Empty description="暂无商品" >
                    </Empty>
                    :
                    null
            }
            <View className='safa-rect' />
        </ScrollView>
        <View className='page-bottom'>
            <Checkbox value={allChecked} onChange={(e) => {
                setAllChecked(e.detail);
                setColdAllChecked(e.detail);
                setCommonChecked(e.detail);
                if (coldChainCart.cartList && coldChainCart.cartList.length) {
                    coldChainCart.cartList.map(item => {
                        item.checked = e.detail;
                    })
                    setColdChainCart({
                        ...coldChainCart,
                        cartList: [...coldChainCart.cartList]
                    })
                }
                if (commonCart.cartList && commonCart.cartList.length) {
                    commonCart.cartList.map(item => {
                        item.checked = e.detail;
                    })
                    commonCart.cartList.map(item => {
                        item.checked = e.detail;
                    })
                    setCommonCart({
                        ...commonCart,
                        cartList: [...commonCart.cartList]
                    })
                }
            }}>
                全选
            </Checkbox>
            <View style={{ marginLeft: "40px" }}>合计：{count}</View>
            <View className="order-bottom" onClick={() => goLink()}>

                <View className="order-price">
                    ¥{totalPrice?.toFixed(2) || 0}
                </View>
                <View className="order-btn">
                    确认下单
                </View>
            </View>
        </View>

        {/* <SubmitBar
            price={(totalPrice + 0.2) * 100}
            renderPrice={() => {
                console.log('ss')
            }}
            renderTip={
            }
            buttonText="提交订单"
            onSubmit={() => {
                goLink();
            }}
        /> */}
    </View>)
}