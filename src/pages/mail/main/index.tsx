import { <PERSON>, <PERSON><PERSON>, Text, ScrollView } from '@tarojs/components'
// import CodeCreator from "taro-code-creator"
import React, { useEffect, useState } from 'react'
import { Field, NavBar, Icon } from '@antmjs/vantui'
import { MiniBack, MiniNavigateTo } from '@/util/route'
import './index.less'
import GoodItem from '@/components/good-item'
import api from '@/util/api'
definePageConfig({
    navigationStyle: 'custom',
})

export default () => {

    const [tabs, setTabs] = useState([])
    const [tabIndex, setTabIndex] = useState(0)
    const [goods, setGoods] = useState([
        // { img: '', title: '', price: 9 },
    ]);

    const getMenu = () => {
        api.getCategory({
            method: 'get', data: {
                freightType: 1,
                businessType: 2
            }
        }).then(res => {
            setTabs(res);
            getGoodsList(res[tabIndex].id)
        })
    }

    const getGoodsList = (id) => {
        api.getGoodsList({
            method: 'get',
            data: {
                businessType: 2,
                freightType: 1,
            }
        }).then(res => {
            console.log(res);
            const arr = (res[id] || [])
            // setRightList(arr);
            console.log("arr:", arr)
            setGoods(arr)
        })
    }

    useEffect(() => {
        getMenu()
    }, [])

    return (<View className='main-main-page'>
        <NavBar
            title={<View style={{
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'row',
                border: '1px solid #f5f5f5',
                borderRadius: '20px',
                height: '40px',
                paddingLeft: '10px'
            }}
                onClick={() => {
                    MiniNavigateTo({
                        url: '/pages/mail/search/index'
                    })
                }}
            >
                <Icon name='search' />
                <View style={{ height: '20px', marginLeft: '10px', width: '2px', backgroundColor: '#f5f5f5' }} />
                <Field style={{ height: '30px', width: '140px' }} placeholder='搜索商品' />
            </View>}
            leftArrow
            onClickLeft={() => {
                MiniBack();
            }}
        />
        <View className='mail-head'>
            <View className='mail-head-left'>
                <View className='mail-head-text1'>ONLINE SHOP</View>
                <View className='mail-head-text2'>线上商店</View>
            </View>
            <View className='mail-head-right'
                onClick={() => {
                    MiniNavigateTo({
                        url: '/pages/mail/cart/index'
                    })
                }}
            >
                <Icon name='goods-collect-o' size={50} />
                <View className='mail-head-text3'>我的购物车</View>
            </View>
        </View>
        <View>
            <ScrollView scrollX={true}>
                <View className='mail-menu-list'>
                    {
                        tabs.map((item, index) => {
                            return (<View className='mail-menu-item'
                                onClick={() => {
                                    setTabIndex(index)
                                    getGoodsList(item.id);
                                }}
                            >
                                <View className='mail-menu-item-title'>{item.name}</View>
                                {index === tabIndex ? <View className='mail-menu-item-line'></View> : null}
                            </View>)
                        })
                    }
                </View>
            </ScrollView >
            <View className='mail-content'>
                {
                    <ScrollView>
                        <View className='scroll-container'>
                            {
                                goods.map((item, index) => {
                                    return <GoodItem
                                        goodItem={item}
                                        key={index}
                                        size={'small'} />
                                })
                            }
                        </View>

                    </ScrollView>
                }

            </View>
        </View >
    </View >)
}