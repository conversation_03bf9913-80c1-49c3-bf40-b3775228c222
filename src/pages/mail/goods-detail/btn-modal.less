.goods-attribute-list{
    .good-img{
        width: 160px;
        height: 160px;
        margin-right: 20px;
        border-radius: 20px;
    }
    .good-name{font-size: 30px;}
    
    .selected{
        border-color: rgb(230, 116, 116);
        border: 2px solid rgb(230, 116, 116) !important;
        color: rgb(230, 116, 116);
    }
    .menu-item{
        padding: 0;
        border: 2px solid  rgb(248, 246, 246);
        border-radius: 10px;
        margin-right: 20px;
        margin-top: 10px;
        // color: rgb(230, 116, 116);
        background-color: rgb(248, 246, 246);
        position: relative;
        width: 30%;
        box-sizing: border-box;
        .spe-name{
            width: 100%;
            padding: 0 10px;
            box-sizing: border-box;
            word-break: break-all;
            display: -webkit-box; /* 将元素设置为弹性盒子 */
                -webkit-box-orient: vertical; /* 设置文本垂直排列 */
                -webkit-line-clamp: 2; /* 限制显示 2 行 */
                overflow: hidden; /* 超出部分隐藏 */
                text-overflow: ellipsis; /* 超出部分显示省略号 */
        }
    }
    .item-disabled{
        opacity: 0.5;
        color: #666;
    }
    .attrs-menu-item{
        // padding: 20px;
        margin-right: -18px;
        position: relative;
        border: 2px solid  rgb(248, 246, 246);
        border-radius: 10px;
        
        margin-top: 10px;
        box-sizing: border-box;
        // color: rgb(230, 116, 116);
        // background-color: rgb(248, 246, 246);
        position: relative;
    }
    .attrs-item{
        padding: 20px;
        position: relative;
        // border: 2px solid  rgb(248, 246, 246);
        border-radius: 10px;
        margin-right: 20px;
        margin-top: 10px;
        // color: rgb(230, 116, 116);
        // background-color: rgb(248, 246, 246);
        position: relative;
    }
    .tag-add-price{
        // position: absolute;
        // right: 0;
        // top: 0;
        font-size: 24px;
        margin-left: 10px;
    }

    .good-num-cell{
        width: 100% !important;
        margin-top: 20px;
        margin-bottom: 20px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
       
    }
    .good-num-cell-1{
        // margin-top: 10px;
        // margin-bottom: 10px;
        padding: 5px 10px;
        // height: 30px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    .good-num-cell-2{
        padding: 10px;
        height: 30px;
        box-sizing: content-box;
        line-height:28px;
    }
    .product-info-btn{
            margin: 0 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            // background-color: #f1f1f1;
            border: 1px solid #4b5c10;
            color: #444;
            font-size: 24px;
        }
    .attr-item{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
    }
    .attr-item,.attr-menu-item{
        padding: 10px;
        position: relative;
        border: 2px solid  rgb(248, 246, 246);
        border-radius: 10px;
        
        margin-top: 10px;
        box-sizing: border-box;
        // color: rgb(230, 116, 116);
        background-color: rgb(248, 246, 246);
        position: relative;
        .attr-item-title{
            font-size: 24px;
            margin-bottom: 10px;
        }
        .attr-item-vals{
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            align-items: center;
            .spe-name{
                display: -webkit-box; /* 将元素设置为弹性盒子 */
                -webkit-box-orient: vertical; /* 设置文本垂直排列 */
                -webkit-line-clamp: 2; /* 限制显示 2 行 */
                overflow: hidden; /* 超出部分隐藏 */
                text-overflow: ellipsis; /* 超出部分显示省略号 */
            }
        }
        .attr-item-vals::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
          }
    }
    .popup-bottom{
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 10px;
        .popup-bottom-price{
            font-size: 30px;
        }
        .popup-bottom-btn{
            margin: 0;
            background-color: #4b5c10;
            padding-left: 20px;
            padding-right: 20px;
            color: #fff;
            .van-button__text{
                color: #fff;
            }
        }
    }
}