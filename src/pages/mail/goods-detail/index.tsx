import { Swiper, SwiperItem, View, Text, Image, RichText, Button } from "@tarojs/components";
import "./index.less";
import { useEffect, useState } from 'react';
import { Icon } from "@antmjs/vantui";
import Taro, { useRouter, useShareAppMessage } from "@tarojs/taro";
import api from "@/util/api";
import { MiniNavigateTo } from "@/util/route";
import BtnModal from "./btn-modal";
definePageConfig({
    navigationBarTitleText: '商品详情'
})
export default () => {
    const [detail, setDetail] = useState({});
    const router = useRouter();
    const [show, setShow] = useState(false);
    const [mode, setMode] = useState('cart')
    const getDetail = () => {
        api.getGoodDetail({
            type: 'get',
            data: {
                id: router.params.id
            }
        }).then(res => {
            setDetail(res);
        })
    }
    useShareAppMessage(callback => {
        return {
            title: "商品详情",
            // path: `/pages/coupon-assist/index?shareId=${id}`,
        };
    });
    useEffect(() => {
        getDetail();
    }, [])
    return (<View className="detail-page">
        <Swiper className="detail-swiper">
            <SwiperItem>
                <Image src={detail?.info?.picUrl} style={{ width: '100%', height: '100%' }} mode="aspectFill" />
            </SwiperItem>
        </Swiper>
        <View className="detail-head">
            <Text className="detail-name">{detail?.info?.name || ""}
            </Text>
            <View className="detail-price">
                <Text>¥{detail?.info?.retailPrice || 0}</Text>

                <Button className="icon-item"
                    openType="share"
                >
                    <Icon size={40} name='share' />
                    分享
                </Button>
            </View>
        </View>
        <View className="detail-content">
            <View className="detail-title">商品详情</View>
            <RichText nodes={detail?.info?.detail} style={{ width: '100%' }}></RichText>
            {/* <Icon size={80} name='share' /> */}

        </View>
        <View className="fix-bottom">
            <View className="fix-left">
                <View className="icon-item" onClick={() => {
                    MiniNavigateTo({
                        url: '/pages/mail/main/index'
                    })
                }}>
                    <Icon size={40} name='shop-o' />
                    <View>商城</View>
                </View>
                <View className="icon-item" onClick={() => {
                    MiniNavigateTo({
                        url: '/pages/mail/cart/index'
                    })
                }}>
                    <Icon size={40} name='cart-o' />
                    <View>购物车</View>
                </View>

            </View>
            <View className="btn-rect">
                <View className="btn-item" onClick={() => {
                    setMode('cart');
                    setShow(true)
                }}>
                    加入购物车
                </View>
                {/* <View className="btn-line"></View>
                <View className="btn-item" onClick={() => {
                    setMode('submit');
                    setShow(true)
                }}>
                    立即购买
                </View> */}
            </View>
        </View>
        <BtnModal mode={mode} goodsItem={detail}
            show={show}
            closeFn={() => {
                setShow(false)
            }}
        />
    </View>)
}