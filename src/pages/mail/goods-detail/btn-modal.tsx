import react, { useEffect, useState } from "react";
import { View, Image, Text, ScrollView } from "@tarojs/components";
import { Empty, Icon, Popup, Button } from "@antmjs/vantui";
import { MiniObject } from "src/type/common";
import api from "@/util/api";
import './btn-modal.less'
import MiniToast from "@/util/toast";
export default function ({
    goodsItem,
    mode,
    // reload
    show,
    closeFn
}) {
    const [goodsAttributes, setgoodsAttributes] = useState([])
    const [checkedCount, setcheckedCount] = useState(1);
    const [totalPrice, settotalPrice] = useState(0.00);
    const [isMenu, setIsMenu] = useState(false);
    const selectAttrValues = (attrIndex, attrValueIndex, val) => {
        // const selectNumber = goodsAttributes[attrIndex].selectNumber;
        const { goodsSpecifications, selectNumber, selectedNum = 0 } = goodsAttributes[attrIndex]
        goodsSpecifications.map((item, index) => {
            if (isMenu) {
                if (val) {
                    if (selectedNum && selectedNum >= selectNumber) {
                        return MiniToast.info("已超过可选件数")
                    }
                    if (index === attrValueIndex) {
                        goodsSpecifications[attrValueIndex].selected = true;
                        goodsSpecifications[attrValueIndex].number = 1;
                        goodsAttributes[attrIndex].selectedNum = !selectedNum ? 1 : selectedNum + 1
                        // priceAll += goodsSpecifications[attrValueIndex].price * 1;
                    }

                } else {
                    if (index === attrValueIndex) {
                        goodsSpecifications[attrValueIndex].selected = false
                        goodsAttributes[attrIndex].selectedNum -= goodsSpecifications[attrValueIndex].number;
                        goodsSpecifications[attrValueIndex].number = 0;
                    }
                }
                console.log("当前选中的数量：", goodsAttributes[attrIndex].selectedNum)
            } else {
                if (val) {
                    if (attrValueIndex !== index) {
                        item.selected = !val;
                    } else {
                        item.selected = val;
                    }
                } else {
                    item.selected = val;
                }
            }

        })
        // goodsAttributes[attrIndex].goodsSpecifications[attrValueIndex].selected = val;
        changePrice(checkedCount)
        setgoodsAttributes([...goodsAttributes])
    }

    const changePrice = (num) => {

        if (!isMenu) {
            let resultNum = num * goodsItem.info.retailPrice;
            goodsAttributes.map((item) => {
                item.goodsSpecifications.map((speItem) => {

                    if (speItem
                        .selected && speItem.price > 0 || speItem.price < 0) {
                        // speItem.price = -0.01
                        resultNum += +((num * Number(speItem.price)).toFixed(2))
                    }
                })
            })
            settotalPrice(resultNum);
        } else {
            let singleMenuPrice = goodsItem.info.retailPrice;
            console.log("singleMenuPrice", goodsItem.info.retailPrice)
            goodsAttributes.map((item) => {
                console.log("item");
                item.goodsSpecifications.map((speItem) => {
                    // console.log("speItem:", speItem)
                    if (speItem.selected) {
                        console.log("speItem:", speItem)
                        singleMenuPrice += (speItem.number || 1) * Number(speItem.price)
                    }
                })
            });
            settotalPrice(num * singleMenuPrice)
        }
    }

    const addCart = () => {
        // goodsAttributes.
        if (!isMenu) {
            for (let arrIndex = 0; arrIndex < goodsAttributes.length; arrIndex++) {
                const item = goodsAttributes[arrIndex];
                // if (item.required)
                // 
                let bol = false;
                for (let valIndex = 0; valIndex < item.goodsSpecifications.length; valIndex++) {
                    if (item.goodsSpecifications[valIndex].selected) {
                        bol = true;
                        break
                    }
                }
                if (item.required && bol === false) {

                    // break;
                    MiniToast.info(`${item.value} 是必选的`)
                    return
                }
            }
        } else {
            for (let arrIndex = 0; arrIndex < goodsAttributes.length; arrIndex++) {
                const item = goodsAttributes[arrIndex];
                if ((item.selectedNum || 0) < item.selectNumber) {
                    MiniToast.info(`${item.value}还需选择${item.selectNumber - (item.selectedNum || 0)}件商品`)
                    return;
                }
            }
        }

        api.cartAdd({
            data: {
                goodsId: goodsItem.info.id,
                number: checkedCount,
                goodsAttributes: goodsAttributes,
                businessType: 2
            },
            method: 'post',
        }).then(res => {
            closeFn(true, goodsItem.id, (goodsItem.checkedCount || 0) + checkedCount);
            // updatebase(productId, number)
            // getCarts();
            MiniToast.success("加购成功")
        })
    }

    const menuChangeCount = (menuIndex, speIndex, count) => {
        const selectNumber = goodsAttributes[menuIndex].selectNumber;
        let counts = 0;
        goodsAttributes[menuIndex].goodsSpecifications.map((item, index) => {
            if (item.selected) {
                if (index === speIndex) {
                    counts += count;
                } else {
                    counts += (item.number || 1);
                }
            }
        })
        console.log("counts:", counts)
        if (counts > selectNumber) {
            return MiniToast.info('已超过可选件数上限')
        }
        goodsAttributes[menuIndex].selectedNum = counts;
        goodsAttributes[menuIndex].goodsSpecifications[speIndex].number = count;
        changePrice(checkedCount)
        setgoodsAttributes([...goodsAttributes])
    }


    useEffect(() => {
        if (show) {
            const arr = [...goodsItem.goodsAttributes];
            let resultPrice = 1 * goodsItem.info.retailPrice;
            let bol = false;
            arr.map((item) => {
                if (item.selectNumber > 0) {
                    bol = true;
                }
                let min = -1;
                item.goodsSpecifications.map((obj, index) => {
                    console.log('item:', item)
                    if (item.required && obj.enable && min < 0) {
                        console.log("item:", item)
                        min = index;
                        obj.selected = true;
                        obj.selected = true
                        resultPrice += +((Number(obj.price)).toFixed(2))
                        if (bol) {
                            obj.number = 1;
                            item.selectedNum = 1;
                        }
                    } else {
                        obj.selected = false
                    }
                })
            })
            setIsMenu(bol);
            setgoodsAttributes(arr || []);
            // setcheckedCount(goodsItem?.checkedCount);
            // settotalPrice((goodsItem.retailPrice).toFixed(2))
            // changePrice(1)
            settotalPrice(resultPrice)
        } else {
            setgoodsAttributes([]);
            setcheckedCount(1);
            settotalPrice(0.00)
        }
    }, [show])
    return (
        <Popup position='bottom' show={show} onClose={() => { closeFn && closeFn() }}
            round
        >
            <View
                style={{
                    // width: '300px',
                    width: '100%',
                    boxSizing: 'border-box',
                    display: 'flex',
                    padding: isMenu ? '20px' : '40px',
                    flexDirection: 'column',
                    alignItems: 'center',
                    // justifyContent: 'center',
                    borderRadius: '64px'
                }}
                className="goods-attribute-list"
            >
                <View style={{
                    display: 'flex',
                    flexDirection: 'row',
                    // alignItems: 'center'
                    width: '100%',

                }}>
                    <Image src={goodsItem?.info?.picUrl} className="good-img" mode="scaleToFill" />
                    <Text className="good-name">{goodsItem?.info?.name}</Text>
                </View>
                <View className="good-num-cell">
                    <Text>数量</Text>
                    <View style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}>
                        {checkedCount > 0 && (
                            <View onClick={() => {
                                // handleDecrease(goodsItem.id, goodsItem, 'productId')
                                setcheckedCount(checkedCount - 1);
                                changePrice(checkedCount - 1)
                            }} className="product-info-btn">
                                -
                            </View>
                        )}
                        {checkedCount > 0 && <View>{checkedCount}</View>}
                        <View onClick={() => {
                            // addIncrease(goodsItem.id, goodsItem)
                            setcheckedCount(checkedCount + 1);
                            changePrice(checkedCount + 1)
                        }} className="product-info-btn">
                            +
                        </View>
                    </View>
                </View>
                {
                    goodsAttributes.length > 0 && <ScrollView

                        style={isMenu ? {
                            height: "400px",
                            width: '100%'
                        } : {
                            width: '100%',
                            height: '150px'
                        }}
                        scroll-y scroll-with-animation
                    >
                        <View>
                            {
                                goodsAttributes.map((item, index) => {
                                    return (
                                        <View className={isMenu ? "attrs-menu-item" : "attrs-item"}>

                                            <View className="attr-item-title">
                                                {item.value}
                                                {item.required ? <Text style={{ color: 'red' }}>(*必选)</Text> : null}
                                                {item.selectNumber > 0 ? <Text>（任选{item.selectNumber}件,</Text> : null}
                                                {item.selectNumber > 0 ? <Text>
                                                    已选<Text>{item?.selectedNum || 0}）</Text>
                                                </Text> : null}
                                            </View>
                                            <View className="attr-item-vals"
                                                style={{
                                                    display: 'flex',
                                                    flexDirection: 'row',
                                                    flexWrap: 'wrap',
                                                    // overflowX: 'scroll'
                                                }}
                                            >

                                                {
                                                    item?.goodsSpecifications.map((speItem, speIndex) => {
                                                        return (
                                                            <View onClick={() => {
                                                                if (!speItem.enable) return
                                                                selectAttrValues(index, speIndex, !speItem.selected)
                                                                // changePrice(checkedCount)
                                                            }}
                                                                className={`${isMenu ? 'menu-item' : 'attr-item'} ${speItem.selected ? 'selected' : 'default'} ${!speItem.enable ? 'item-disabled' : ''}`}

                                                            >
                                                                {
                                                                    isMenu ?
                                                                        <Image src={speItem.picUrl}
                                                                            style={{
                                                                                width: '100%',
                                                                                height: '100px'
                                                                            }}
                                                                        />
                                                                        :
                                                                        null
                                                                }
                                                                <View className="spe-name" style={{
                                                                    justifyContent: 'space-between'
                                                                }}>
                                                                    <Text>{speItem.value}</Text>
                                                                </View>
                                                                {
                                                                    isMenu && speItem.selected ?
                                                                        <View className="good-num-cell-1">
                                                                            {/* <Text>数量</Text> */}
                                                                            <View style={{
                                                                                display: 'flex',
                                                                                flexDirection: 'row',
                                                                                alignItems: 'center',
                                                                                // padding: '10px',
                                                                                boxSizing: 'content-box'
                                                                            }}>
                                                                                {/* {checkedCount > 0 && ( */}
                                                                                <View onClick={(e) => {
                                                                                    if (speItem.number - 1 <= 0) {
                                                                                        selectAttrValues(index, speIndex, false)
                                                                                    } else {
                                                                                        menuChangeCount(index, speIndex, speItem.number - 1)
                                                                                    }
                                                                                    e.stopPropagation();

                                                                                }} className="product-info-btn">
                                                                                    -
                                                                                </View>
                                                                                <View>{speItem.number || 1}</View>
                                                                                <View onClick={(e) => {
                                                                                    // e.stopPropagation()
                                                                                    console.log("number", speItem.number)
                                                                                    const num = !speItem.number ? 2 : speItem.number + 1;
                                                                                    console.log("num:", num)
                                                                                    menuChangeCount(index, speIndex, num)
                                                                                    e.stopPropagation();
                                                                                }} className="product-info-btn" style={{}}>
                                                                                    +
                                                                                </View>
                                                                            </View>
                                                                        </View>
                                                                        :
                                                                        speItem.price ?
                                                                            <Text className="good-num-cell-2">{speItem.price}</Text>
                                                                            :
                                                                            null
                                                                }

                                                            </View>
                                                        )
                                                    })
                                                }
                                            </View>
                                        </View>)
                                })
                            }
                        </View>
                    </ScrollView>
                }

                <View className="popup-bottom">
                    <Text className="popup-bottom-price">¥{totalPrice.toFixed(2)}</Text>
                    <Button
                        className="popup-bottom-btn"
                        icon="shopping-cart-o" type="primary"
                        onClick={() => { addCart() }}
                    >
                        确定
                    </Button>
                </View>
            </View>

        </Popup >
    )
}