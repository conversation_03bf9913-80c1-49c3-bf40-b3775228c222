.detail-page{
    position: relative;
    min-height: 100vh;
    padding-bottom: calc(100px + env(safe-area-inset-bottom));
    .detail-swiper{
        background-color: #f5f5f5;
        height: 750px;
        width: 750px;
    }
    .detail-head{
        padding: 30px;
        .detail-name{
            font-size: 48px;
            word-break: break-word;
        }
        .detail-price{
            width: 100%;
            font-size: 50px;
            color: @primaryColor;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
    }
    .detail-content{
        margin: 0 30px;
        border-top: 1px solid #ccc;
        padding-top: 30px;
        .detail-title{
            font-size: 40px;
            margin-bottom: 40px;
        }
    }
    .icon-item{
        width: 180px;
        border: none;
        outline: none;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 0;
        padding-left: 0;
        margin-right: 0;
       
    } 
    .icon-item::after {
        display: none;
    }
    .fix-bottom{
        position: fixed;
        bottom: 0;
        padding-bottom: calc(20px + env(safe-area-inset-bottom));
        left: 0;
        right: 0;
        padding-left: 40px;
        padding-right: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 20px;
        box-shadow: 0 5px 10px #ccc;
        background-color: #fff;
        .fix-left{
            display: flex;
            flex-direction: row;
        }
        .btn-rect{
            background-color: @primaryColor;
            color: #fff;
            height: 80px;
            border-radius: 40px;
            padding-left: 34px;
            padding-right: 34px;
            display: flex;
            flex-direction: row;
            align-items: center;
            .btn-line{
                width: 2px;
                height: 40px;
                background-color: #fff;
                margin-left: 10px;
                margin-right: 10px;
            }
        }
    }
}
