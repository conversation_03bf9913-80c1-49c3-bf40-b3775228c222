import { <PERSON>, <PERSON><PERSON>, Text, ScrollView } from '@tarojs/components'
// import CodeCreator from "taro-code-creator"
import React, { useEffect, useState } from 'react'
import { Field, NavBar, Icon, Search } from '@antmjs/vantui'
import { MiniBack, MiniNavigateTo } from '@/util/route'
import './index.less'
import api from '@/util/api'
import { clearStorageSync, getStorageSync, setStorageSync } from '@tarojs/taro'
import MiniToast from '@/util/toast'
definePageConfig({
    navigationBarTitleText: '7 RIVERLIGHT',
})

export default () => {
    const [search, setSearch] = useState('');
    const [list, setList] = useState<string[]>([]);

    const searchResult = (val) => {
        api.searchGoods({
            type: 'get',
            data: {
                goodsName: val,
            }
        }).then(res => {
            console.log("res:", res);

            if (res.length > 0) {
                if (!list.includes(val)) {
                    list.push(val);
                    setList([...list])
                }
                setStorageSync('SearchContent', JSON.stringify(list))
                MiniNavigateTo({
                    url: '/pages/mail/search-result/index?name=' + val
                })
            } else {
                MiniToast.info('很抱歉，未找到相关结果')
            }
        }).catch(err => {
            console.log("Err:", err);
        })
    }
    useEffect(() => {
        console.log(getStorageSync("SearchContent"))
        if (getStorageSync("SearchContent")) {
            // setList(JSON.parse(getStorageSync("SearchContent")))
            try {
                const arr = JSON.parse(getStorageSync("SearchContent"))
                setList(arr || []);
            } catch (error) {
                console.log(error)
            }
        }
    }, [])
    return (<View>
        <Search value={search} onChange={(e) => {
            setSearch(e.detail.value)
        }} placeholder="请输入商品名称"
            onSearch={(e) => {
                console.log(e);
                const val = e.detail
                searchResult(val)
            }}
        />
        {
            list.length > 0 ?
                <View className='history-rect'>
                    <View className='history-title'>
                        <View className='history-title-text'>搜索历史</View>
                        <View className='history-title-btn'
                            onClick={() => {
                                clearStorageSync('SearchContent');
                                setList([])
                            }}
                        >清除历史</View>
                    </View>
                    <View className='history-list'>
                        {list.map(item => {
                            return (<View className='history-item' onClick={() => {
                                searchResult(item);
                            }} >{item}</View>)
                        })}
                    </View>

                </View>
                :
                null

        }

    </View>)
}