import { <PERSON>, <PERSON><PERSON>, Text, ScrollView } from '@tarojs/components'
// import CodeCreator from "taro-code-creator"
import React, { useEffect, useState } from 'react'
import { Field, NavBar, Icon } from '@antmjs/vantui'
import { MiniBack, MiniNavigateTo } from '@/util/route'
import './index.less'
import GoodItem from '@/components/good-item'
import api from '@/util/api'
import { useRouter } from '@tarojs/taro'
definePageConfig({
    navigationBarTitleText: '搜索结果',
})

export default () => {

    const [goods, setGoods] = useState([]);
    const route = useRouter();
    const searchResult = (val) => {
        api.searchGoods({
            type: 'get',
            data: {
                goodsName: val,
            }
        }).then(res => {
            // console.log("res:", res);
            setGoods(res);
        }).catch(err => {
            console.log("Err:", err);
        })
    }
    useEffect(() => {
        searchResult(route.params.name);
    }, [])

    return (<View className='search-result'>
        <View className='title'>{route.params.name}</View>
        <View className='mail-content'>
            {
                <ScrollView>
                    <View className='scroll-container'>
                        {
                            goods.map((item, index) => {
                                return <GoodItem
                                    goodItem={item}
                                    key={index}
                                    size={'small'} />
                            })
                        }
                    </View>

                </ScrollView>
            }

        </View>
    </View >)
}