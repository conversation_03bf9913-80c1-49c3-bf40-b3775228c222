import { Image, View, Text, Input, But<PERSON>, PageContainer } from "@tarojs/components"
import './index.less'
import { useEffect, useState } from "react"
import api from "@/util/api"
import Taro, { useUnload, useDidShow, useRouter } from "@tarojs/taro"
import { MiniNavigateTo, MiniRedirectTo } from "@/util/route"
import MiniToast from "@/util/toast"
import { MiniObject } from "src/type/common"
import { observer, useObserver } from "mobx-react-lite";
import confirmOrderStore from "@/store/confirmOrder"
// import RemarkModal from "./remark-modal"
import { Icon } from "@antmjs/vantui"
import LimitTimePicker from "@/components/limit-time-picker"
import shopStore from "@/store/shopInfo"
import "./index.less"
import RemarkModal from "../../order/confirm-order/remark-modal"
definePageConfig({
    navigationBarTitleText: '订单结算',
    navigationBarTextStyle: 'white',
    // backgroundColor: '#a1a789'
    navigationBarBackgroundColor: '#a1a789'
})
export default observer(() => {
    const route = useRouter();
    const store = confirmOrderStore.getAddress();
    // const coupon = confirmOrderStore.getCoupon();
    const [address, setAddress] = useState<MiniObject>(store)
    const [remarks, setRemarks] = useState('')
    const [shops, setShops] = useState<any[]>([])
    const [orderTotal, setOrderTotal] = useState(0.00)
    const [remarkShow, setRemarkShow] = useState(false)
    const [goodsTotalPrice, setGoodsTotalPrice] = useState(0)
    const [goodsTotalNum, setgoodsTotalNum] = useState(0)
    const [freightPrice, setFreightPrice] = useState(0)
    const [packingFee, setPackingFee] = useState(0);
    const [coldChainResVo, setColdChainResVo] = useState();
    const [commonResVo, setCommonResVo] = useState();
    const [other, setOther] = useState({
        allowSubmitOrder: true,
        reason: '',
        couponUserId: null
    })
    const submitOrder = () => {
        if (!other?.allowSubmitOrder) {
            return MiniToast.info(other.reason)
        }
        if (!address.id && !address.addressId) {
            return MiniToast.info("请选择配送地址")
        }
        let coldChainSubmit = null;
        if (coldChainResVo) {
            let obj = {};
            coldChainSubmit = {
                freightPrice: coldChainResVo.freightPrice
            };
            coldChainResVo.checkedGoodsList.map(item => {
                obj[item.id] = "";
            })
            coldChainSubmit.cartIdRemarkMap = obj
        }
        let commonSubmit = null;
        if (commonResVo) {
            let obj = {};
            commonSubmit = {
                freightPrice: commonResVo.freightPrice
            };
            commonResVo.checkedGoodsList.map(item => {
                obj[item.id] = "";
            })
            commonSubmit.cartIdRemarkMap = obj
        }
        console.log(address)
        api.submitMailOrder({
            data: {
                addressId: address.id || address.addressId,
                // freightPrice: freightPrice
                // packingFee: packingFee
                coldChainSubmit: coldChainSubmit,
                commonSubmit: commonSubmit,
                remarks: remarks
            },
            method: 'POST',
        }).then(res => {
            MiniRedirectTo({
                url: '/pages/order/cashier/index?coldChainOrderId=' + (res.coldChainOrderId || "") + "&commonOrderId=" + (res.commonOrderId || "")
            });
        }).catch((err) => {

        })
    }

    const getDetail = () => {
        try {
            //@ts-ignore
            const commonList = JSON.parse(route.params.commonList);
            //@ts-ignore
            const coldList = JSON.parse(route.params.coldList);
            // const 
            // commonList = [] & coldList=[1309]
            console.log("address:", address)
            const addressObj = confirmOrderStore.getAddress();
            api.preShopOrder({
                data: {
                    shopId: 1,
                    cartIdList: [...coldList, ...commonList],
                    addressId: addressObj.id || addressObj.addressId,
                    // freightType: +freightType,
                    // couponUserId: couponUserId,
                },
                method: 'post',
            }).then(res => {
                console.log(res)
                setColdChainResVo(res.coldChainResVo)
                setCommonResVo(res.commonResVo)

                // setShops(res.checkedGoodsList || [])
                setOther({
                    allowSubmitOrder: res.allowSubmitOrder,
                    reason: res.reason,
                    couponUserId: res.couponUserId
                })
                setOrderTotal(res.totalActualPrice);

            }).catch((err) => {

            })
        } catch (error) {

        }

    }


    useDidShow(() => {
        setAddress(confirmOrderStore.getAddress());
        // setCoupon(confirmOrderStore.getCoupon());
        getDetail();
    })

    useUnload(() => {
        console.log("页面关闭了")
        confirmOrderStore.setCoupon({})
    })

    // console.log("coupon:", coupon)

    return (<View className="confirm-order">
        {
            address?.id ?
                <View className={"address-wrapper"}
                    onClick={() => {
                        MiniNavigateTo({
                            url: '/pages/order/address/index?type=select&from=order&businessType=2'
                        })
                    }}
                >
                    <View className="address_detail">
                        {address.addressLocation}
                    </View>
                    <View className="usr_info">
                        <View className="usr_name">{address.name}</View>
                        <View className="usr_tel">{address.mobile}</View>
                    </View>
                </View>
                :
                <View className="address-wrapper-empty" onClick={() => {
                    MiniNavigateTo({
                        url: '/pages/order/address/index?type=select&from=order&businessType=2'
                    })
                }}>
                    <Text className="address-wrapper-empty-text">请选择收货地址</Text>
                    <Icon name="edit" size={30} />
                </View>
        }

        <View className="content">
            {
                commonResVo &&
                <View className="order-content">
                    <View className="goods-title">普快运输</View>
                    <View className="goods">
                        {
                            commonResVo.checkedGoodsList.map((shop: any) => (
                                <View className="goods-item">
                                    <Image src={shop.picUrl} mode='aspectFill' className="goods-item-img" />
                                    <View className="goods-item-content">
                                        <View className="goods-item-left">
                                            <View className="goods-item-name">
                                                {shop.goodsName}
                                            </View>
                                            <View className="goods-item-spe">{shop.specifications}</View>
                                        </View>

                                        <View className="goods-item-right">
                                            {/* <View className="goods-item-price">¥{shop.price}</View> */}
                                            {
                                                shop.discountFlag ?
                                                    <View >
                                                        <View className="goods-item-price" style={{ textDecoration: 'line-through' }}>￥{shop.price}</View>
                                                        <View className="goods-item-price" style={{ marginLeft: '8px', color: 'red' }}>¥{shop.discountPrice}</View>
                                                    </View>
                                                    :
                                                    <View className="goods-item-price">￥{shop.price}</View>

                                            }
                                            <View className="goods-item-count">x{shop.number}</View>
                                        </View>
                                    </View>
                                </View>
                            ))
                        }
                    </View>
                    <View className="order-item">
                        <View className="order-item-title">
                            <Icon name='shopping-cart-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                            商品总价</View>
                        <View className="order-item-content">
                            <View className="order-item-empty">¥{commonResVo.goodsTotalPrice || 0.00}</View>
                        </View>
                    </View>
                    <View className="order-item">
                        <View className="order-item-title">
                            <Icon name='balance-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                            运费</View>
                        <View className="order-item-content">
                            <View className="order-item-empty">¥{commonResVo.freightPrice || 0.00}</View>
                        </View>
                    </View>
                    <View className="order-total">
                        <View className="order-total-title"></View>
                        <View className="order-total-price">
                            共 {commonResVo.checkedGoodsList.length}件商品   合计  ¥{(commonResVo.goodsTotalPrice + commonResVo.freightPrice)?.toFixed(2) || 0}
                        </View>
                    </View>

                </View>
            }
            {
                coldChainResVo &&
                <View className="order-content">
                    <View className="goods-title">冷链运输</View>
                    <View className="goods">
                        {
                            coldChainResVo.checkedGoodsList.map((shop: any) => (
                                <View className="goods-item">
                                    <Image src={shop.picUrl} mode='aspectFill' className="goods-item-img" />
                                    <View className="goods-item-content">
                                        <View className="goods-item-left">
                                            <View className="goods-item-name">
                                                {shop.goodsName}
                                            </View>
                                            <View className="goods-item-spe">{shop.specifications}</View>
                                        </View>

                                        <View className="goods-item-right">
                                            {/* <View className="goods-item-price">¥{shop.price}</View> */}
                                            {
                                                shop.discountFlag ?
                                                    <View >
                                                        <View className="goods-item-price" style={{ textDecoration: 'line-through' }}>￥{shop.price}</View>
                                                        <View className="goods-item-price" style={{ marginLeft: '8px', color: 'red' }}>¥{shop.discountPrice}</View>
                                                    </View>
                                                    :
                                                    <View className="goods-item-price">￥{shop.price}</View>

                                            }
                                            <View className="goods-item-count">x{shop.number}</View>
                                        </View>
                                    </View>
                                </View>
                            ))
                        }
                    </View>
                    <View className="order-item">
                        <View className="order-item-title">
                            <Icon name='shopping-cart-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                            商品总价</View>
                        <View className="order-item-content">
                            <View className="order-item-empty">¥{coldChainResVo.goodsTotalPrice || 0.00}</View>
                        </View>
                    </View>
                    <View className="order-item">
                        <View className="order-item-title">
                            <Icon name='balance-o' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />

                            运费</View>
                        <View className="order-item-content">
                            <View className="order-item-empty">¥{coldChainResVo.freightPrice || 0.00}</View>
                        </View>
                    </View>
                    <View className="order-total">
                        <View className="order-total-title"></View>
                        <View className="order-total-price">
                            共 {coldChainResVo.checkedGoodsList.length}件商品   合计  ¥{(coldChainResVo.goodsTotalPrice + coldChainResVo.freightPrice)?.toFixed(2) || 0}
                        </View>
                    </View>

                </View>
            }
        </View>

        <View className="content-other order-item" onClick={() => {
            setRemarkShow(true)
        }}>
            <View className="order-item-title">备注</View>
            <View className="order-item-remark">{remarks || '口味、包装等特殊要求'}</View>
        </View>
        <View className="order-bottom" onClick={() => submitOrder()}>
            <View className="order-price">
                ¥{orderTotal?.toFixed(2) || 0}
            </View>
            <View className="order-btn">
                确认下单
            </View>
        </View>
        <RemarkModal remark={remarks} show={remarkShow} closeFn={(val) => {
            if (val) {
                setRemarks(val)
            }
            setRemarkShow(false);
        }} />
    </View >)
})