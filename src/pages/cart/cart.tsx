/**
 * @description 购物车
 * <AUTHOR>
 */
import React, { useState, useEffect, useRef, useCallback } from "react";
import { ScrollView, View, Button, Image, Text } from "@tarojs/components";
import "./cart.less";
import { Stepper, Icon, Cell, Popup, Dialog } from "@antmjs/vantui";
import CartPopup from "./components/cart-popup";
import api from "@/util/api";
import { MiniBack, MiniNavigateTo } from "@/util/route";
import MiniToast from "@/util/toast";
import { useDidShow, useShareAppMessage } from "@tarojs/taro";
import confirmOrderStore from "@/store/confirmOrder";
import DetailPopup from "./components/detail-popup";
import Taro from "@tarojs/taro";
import LimitTimePicker from "@/components/limit-time-picker";
import GoodsAttributeList from "./components/goods-attribute-list";
import shopStore from "@/store/shopInfo";
import { observer, useObserver } from "mobx-react-lite";
import userStore from "@/store/user";
import ShopSelector from "@/components/shop-selector";
const Dialog_ = Dialog.createOnlyDialog()
definePageConfig({
    navigationBarTitleText: "购物车",
    enableShareAppMessage: true,
})
const hotImg = require("../../images/WechatIMG5331.jpg");
const newImg = require("../../images/53421743514594_.pic.jpg");
const Cart = observer(() => {
    const [navTop, setNavTop] = useState(0);
    const [goodetail, setGoodetail] = useState<any>(null);
    const [detailShow, setDetailShow] = useState(false);
    const navigation = useRef({ pageNo: 1, pageSize: 20, totalPage: 0 });
    const [activeIndex, setActiveIndex] = useState(0);
    const [errorOpen, setErrorOpen] = useState(false);
    const [errorList, setErrorList] = useState([]);
    const onReachBottom = () => {
        // 大坑只能查两页的数据if (this.showLoading) {return;}//上一个网络请求还没回来，不允许加载
        if (navigation.current.pageNo > navigation.current.totalPage) {
            return;
        } //表示最后一页了，也不允许加载
        // 如果总页数只有1页时，则不查第二页的数据，避免请求两次查询
        if (navigation.current.totalPage != 1) {
            // getHomeData(false, tabcurrent);
        }
    };
    // 左侧tab
    const [leftList, setLeftList] = useState<any[]>([]);
    // 全部商品
    const [allShopList, setAllShopList] = useState({})
    // 右侧商品
    const [rightList, setRightList] = useState<any[]>([]);
    // 已选商品
    const [List, setList] = useState<any>([])

    const [other, setOther] = useState({
        allowSubmitOrder: true,
        reason: ''
    })
    const [note, setNote] = useState('')
    const [totalQuantity, setTotalQuantity] = useState(0);
    const [totalPrice, setTotalPrice] = useState(0);
    const [selectShow, setSelectShow] = useState(false); // 是否显示弹出层
    const [attrShow, setAttrShow] = useState(false);
    const [currentGoodItem, setCurrentGoodItem] = useState(null)
    // 左侧点击tab
    const handleCategoryClick = (categoryId, index) => {
        setActiveIndex(index)
        // setRightList((allShopList[categoryId] || []).map(item => { !item.quantity && (item.quantity = 0) }));
        const arr = (allShopList[categoryId] || []).map(item => item)
        setRightList(arr);
    };
    // 删除商品
    const handleDecrease = (productId, goodsItem, idKey) => {
        const number = idKey === 'goodsId' ? (goodsItem.number > 0 ? goodsItem.number - 1 : 0) : (goodsItem.checkedCount > 0 ? goodsItem.checkedCount - 1 : 0);
        if (idKey === 'goodsId') {
            api.cartUpdate({
                data: {
                    goodsId: goodsItem.goodsId,
                    number: number,
                    id: goodsItem.id,
                },
                method: 'post',
            }).then(res => {
                updatebase(goodsItem.goodsId, number)
                getCarts();
            })
        } else {
            api.goodsUpdate({
                data: {
                    number: number,
                    goodsId: goodsItem.id,
                },
                method: 'post',
            }).then(res => {
                updatebase(goodsItem.id, number)
                getCarts();
            })
        }
    };

    const updatebase = (goodsId, number) => {
        const arr = [...allShopList[leftList[activeIndex].id]];
        const index1 = arr.map(item => item.id).indexOf(goodsId)
        arr[index1].number = number;
        setAllShopList({ ...allShopList });
        const index2 = rightList.map(item => item.id).indexOf(goodsId)
        rightList[index2].checkedCount = number;
        setRightList([...rightList])
    }

    // 添加商品
    const addIncrease = (productId, goodsItem) => {
        // 处理商品数据
        const number = goodsItem.checkedCount ? goodsItem.checkedCount + 1 : 1
        api.cartAdd({
            data: {
                goodsId: productId,
                number: 1,
            },
            method: 'post',
        }).then(res => {
            updatebase(productId, number)
            getCarts();
        })
    };
    // 添加商品
    const handleIncrease = (productId, goodsItem) => {
        const number = goodsItem.number ? goodsItem.number + 1 : 1
        api.cartUpdate({
            data: {
                goodsId: goodsItem.goodsId,
                number: number,
                id: goodsItem.id,
            },
            method: 'post',
        }).then(res => {
            updatebase(goodsItem.goodsId, number)
            getCarts();
        })
    };

    const getMenu = () => {
        api.getCategory({
            method: 'get', data: {
                freightType: confirmOrderStore.freightType
            }
        }).then(res => {
            setLeftList(res);
            getGoodsList(res)
        })
    }

    const getGoodsList = (list) => {
        api.getGoodsList({
            method: 'get', data: {
                freightType: confirmOrderStore.freightType
            }
        }).then(res => {
            setAllShopList(res)
            const arr = (res[list[activeIndex].id] || [])
            setRightList(arr);
        })
    }

    const getCarts = () => {
        userStore.getUser()?.userId && api.getCarts({
            method: 'get',
            data: {
                freightType: confirmOrderStore.freightType
            }
        }).then(res => {
            setList(res.cartList);
            setTotalPrice(res.cartTotal.goodsAmount)
            setTotalQuantity(res.cartTotal.goodsCount)
            setOther({
                allowSubmitOrder: res.allowSubmitOrder,
                reason: res.reason
            })
            setNote(res.note)
            // const errorList = res.errorList || [{ goodsName: '商品名1', number: 2 },
            // { goodsName: '商品名1商品名1商品名1商品名1', number: 2 },
            // ]
            // console.log("errorList:", errorList)
            if (res.errorList && res.errorList.length > 0) {
                // alert(errorList)
                setErrorList(res.errorList);
                setErrorOpen(true);
            }

        })
    }

    useDidShow(() => {
        getCarts();
        getMenu();
    })
    useShareAppMessage(() => {
        return {
            title: '7 RIVERLIGHT',
            path: '/pages/cart/cart'
        }
    })
    return (
        <View className="shop-cart">
            <View className="shop-cart-top">
                {/* 外送显示 */}
                {confirmOrderStore.freightType == '0' && <View>
                    <View style={{ display: 'flex', alignItems: 'center', flexDirection: 'row', padding: '20px 20px 0 20px' }}
                        onClick={() => {
                            MiniBack()
                        }}
                    >
                        {/* 外送显示 */}
                        <Icon name='location' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />
                        {/* 杭州市滨江区星耀城1期1幢221室 */}
                        <View>{confirmOrderStore.getAddress()?.addressLocation || '请选择地址'}</View>
                        <Icon name='arrow' size={'40'} color="#a14e42" style={{ marginRight: '10px' }} />
                    </View>
                    <View style={{ padding: '10px 20px' }}>
                        <Text style={{ fontSize: '12px', color: '#ccc', marginBottom: '8px', display: 'block' }}>配送门店：</Text>
                        <ShopSelector className="cart-page" />
                    </View>
                </View>}
                {/* 到店显示 */}
                {['1', '2'].includes(confirmOrderStore.freightType) && <View>
                    <View style={{ display: 'flex', alignItems: 'center', flexDirection: 'row', padding: '20px 20px 8px 20px ' }}
                    > {/* 到店显示 */}
                        <View style={{ flex: 1 }}>
                            <ShopSelector className="cart-page" />
                        </View>

                        <View className="select-method">
                            <View className={confirmOrderStore.freightType === "1" ? "select-method-item selected" : "select-method-item"}
                                onClick={() => {
                                    confirmOrderStore.setFreightType("1")
                                    getCarts();
                                    getMenu();
                                }}
                            > 自取</View>
                            <View className={confirmOrderStore.freightType === "2" ? "select-method-item selected" : "select-method-item"}
                                onClick={() => {
                                    confirmOrderStore.setFreightType("2")
                                    getCarts();
                                    getMenu();
                                }}
                            > 堂食</View>
                        </View>
                    </View>
                    <View
                        style={{
                            display: 'flex', backgroundColor: "#4b5c10", alignItems: 'center', flexDirection: 'row',
                            padding: '8px',
                            borderRadius: '8px',
                            margin: '0 20px 10px 20px',
                            justifyContent: 'space-between',
                            color: "#fff"
                        }}
                    >
                        <View style={{
                            display: 'flex',
                            alignItems: 'center',
                            flexDirection: 'row'
                        }}>
                            <Icon name='clock-o' size={'28'} color="#fff" style={{ marginRight: '10px' }} />
                            <Text style={{ fontSize: '12px', color: '#fff' }}>预约时间</Text>
                        </View>
                        <LimitTimePicker value={null} onChange={(value) => {
                            console.log("LimitTimePicker", value)
                            confirmOrderStore.setTime(value)
                        }} />
                    </View>
                </View>}
            </View >
            <View className="shop-cart-wrap">
                <ScrollView className="shop-cart-nav" scroll-y scroll-with-animation scroll-top={navTop}>
                    {leftList.map((item, index) => (
                        <View className="shop-cart-nav-item" key={item.id} onClick={() => handleCategoryClick(item.id, index)}>
                            <View>{item.name}</View>
                            <View className={activeIndex === index ? 'active' : 'inactive'}></View>
                            <View className="line"></View>
                        </View>
                    ))}
                </ScrollView>
                <ScrollView
                    className="shop-cart-main"
                    scroll-y
                    scroll-with-animation
                    scroll-into-View={`main-${navTop}`}
                    onScrollToLower={onReachBottom}
                >
                    {rightList.map((item: any) => (
                        <View className="product-item" key={item.id}>
                            <View className="product-img"
                                onClick={() => {

                                    if (Array.isArray(item.goodsAttributes) && item.goodsAttributes.length) {
                                        setAttrShow(true);
                                        setCurrentGoodItem({ ...item })
                                        return
                                    }
                                    setGoodetail(item);
                                    setDetailShow(true)
                                }}
                            >
                                <Image src={item.picUrl} className="product-img" style={{ margin: 0 }} />
                                {
                                    item.isSellOut &&
                                    <View className="product-sell-empty">
                                        本店暂时售罄
                                    </View>
                                }
                                {
                                    item.inventoryNotify &&
                                    <View className="product-sell-empty">
                                        {item.inventoryNotify}
                                    </View>
                                }
                                {/* <Icon name='new-arrival' size="32px" /> */}
                                {/* <Icon name='hot-o' size="32px" style={{ position: 'absolute', right: 0, top: 0, color: '#4b5c10' }} />
                                <Icon name='new-arrival-o' size="32px" style={{ position: 'absolute', left: 0, top: 0, color: '#4b5c10' }} /> */}

                                {/* <Icon name='hot' size="32px" style={{ position: 'absolute', right: "-10px", top: "-10px", color: '#4b5c10' }} /> */}
                                {/* <Icon name='new' size="32px" style={{ position: 'absolute', right: "-10px", top: "0", color: '#4b5c10', backgroundColor: 'rgba(255,255,255,0.2)' }} /> */}
                                {/* <Icon size="32px" style={{ position: 'absolute', right: "-10px", top: "0", color: '#4b5c10', backgroundColor: 'rgba(255,255,255,0.2)' }} /> */}
                                {/* <Image src={hotImg} style={{ position: 'absolute', left: "-4px", top: "-14px", width: '32px', height: '32px' }} /> */}
                                {
                                    item.isNew ?
                                        <Image src={newImg} style={{ position: 'absolute', left: "-4px", top: "-14px", width: '32px', height: '32px' }} />

                                        :

                                        item.isHot ?
                                            <Image src={hotImg} style={{ position: 'absolute', left: "-4px", top: "-14px", width: '32px', height: '32px' }} />
                                            : null

                                }
                            </View>

                            <View className="product-info">
                                <View>{item.name}</View>
                                <View className="product-info-content">
                                    {
                                        item.discountFlag ?
                                            <View style={{ display: 'flex', flexDirection: 'row' }}>
                                                <View style={{ textDecoration: 'line-through' }}>￥{item.retailPrice}</View>
                                                <View style={{ marginLeft: '8px', color: 'red' }}>¥{item.discountPrice}</View>
                                            </View>
                                            :
                                            <View>￥{item.retailPrice}</View>

                                    }

                                    {
                                        !item.isSellOut &&
                                        <View className="product-info-quantity">
                                            {
                                                Array.isArray(item.goodsAttributes) && item.goodsAttributes.length ?
                                                    <>
                                                        <View onClick={() => {
                                                            setAttrShow(true);
                                                            setCurrentGoodItem({ ...item })

                                                        }} className="product-info-btn1">
                                                            选规格
                                                        </View>
                                                        {item.checkedCount > 0 && <View className="product-info-count">{item.checkedCount}</View>}

                                                    </>

                                                    :
                                                    <>
                                                        {item.checkedCount > 0 && (
                                                            <View onClick={() => {
                                                                handleDecrease(item.id, item, 'productId')
                                                            }} className="product-info-btn">
                                                                -
                                                            </View>
                                                        )}
                                                        {item.checkedCount > 0 && <View>{item.checkedCount}</View>}
                                                        <View onClick={() => {
                                                            if (Array.isArray(item.goodsAttributes)) {
                                                                // addIncrease(item.id, item)
                                                                // console.log("item:", item)
                                                                setAttrShow(true);
                                                                setCurrentGoodItem({ ...item })
                                                            } else {
                                                                addIncrease(item.id, item)
                                                            }

                                                        }} className="product-info-btn">
                                                            +
                                                        </View>
                                                    </>
                                            }

                                        </View>
                                    }

                                </View>
                            </View>
                        </View>
                    ))}
                </ScrollView>

            </View>
            <View className="total" style={{
                bottom: 'calc(10px + env(safe-area-inset-bottom))'
            }}>
                <View className="total-left">
                    <Icon name="shopping-cart" className="icon" size="32px"
                        onClick={() => {
                            setSelectShow(true);
                        }} />
                    <View className="text">￥{totalPrice}</View>
                    {confirmOrderStore.freightType == "0" &&
                        <Text style={{ fontSize: '14px', color: '#ccc', marginLeft: '5px' }}>| {note}</Text>
                    }
                </View>
                <View
                    onClick={() => {
                        setSelectShow(true);
                        if (selectShow) {

                            if (List.length === 0) return MiniToast.info('请选择商品')
                            if (confirmOrderStore.freightType === "0" && !other?.allowSubmitOrder) {
                                return MiniToast.info(other.reason)
                            }
                            const str = JSON.stringify(List.map((item) => item.id));
                            setSelectShow(false)
                            MiniNavigateTo({
                                url: `/pages/order/confirm-order/index?carts=${str}`,
                            })
                        }
                    }}
                >
                    选好了 &gt;
                </View>
            </View>
            <CartPopup
                show={selectShow}
                data={List}
                totalQuantity={totalQuantity}
                onClose={(allClear) => {
                    setSelectShow(false);
                    getCarts()
                    if (allClear) {
                        // getCarts();
                        getMenu();
                    }
                }}
                handleDecrease={handleDecrease}
                handleIncrease={handleIncrease}
            />
            <DetailPopup
                data={goodetail}
                show={detailShow}
                onClose={() => {
                    setDetailShow(false);
                }}
                handleIncrease={addIncrease}
            />
            <GoodsAttributeList
                goodsItem={currentGoodItem}
                show={attrShow}
                closeFn={(load, goodsId, number) => {
                    setAttrShow(false);
                    if (load) {
                        console.log("number:", number)
                        updatebase(goodsId, number)
                        getCarts();
                    }
                }}
            />
            {/* 无法购买商品弹框 */}
            <Dialog
                id="vanDialog2"
                title="以下商品无法购买"
                // theme="round-button"
                // theme=""
                selector="vanDialog2"
                // showCancelButton
                // confirmButtonOpenType="getUserInfo"
                show={errorOpen}
                onClose={() => { setErrorOpen(false) }}
            >
                <View style={{
                    padding: '0 20px 20px 20px'
                }}>
                    {
                        errorList.map((item) => (<View style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                        }}>
                            <Text style={{
                                fontSize: '12px',
                                textOverflow: 'ellipsis',
                                width: '50%',
                                // height: '24px',
                                overflow: "hidden",
                                // wordBreak: "keep-all",
                                // wordBreak:''
                            }}>{item.goodsName}</Text>
                            <Text style={{
                                color: '#999',
                                fontSize: '12px',
                            }}>x{item.number}</Text>
                            <Text style={{
                                color: '#999',
                                fontSize: '12px',
                                marginLeft: '10px'
                            }}>{item.errorMsg || '已售罄'}</Text>
                        </View>))
                    }
                </View>
            </Dialog>
        </View >
    );
});

export default Cart;
