/* pages/cart/cart.wxss */
.shop-cart {
    height: 100vh;
    display: flex;
    flex-direction: column;
    .input-placeholder{
        text-align: right;
    }
    &-top{
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: #Fff;
        .select-method{
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #ccc;
            border-radius: 12px;
            // padding: ;
            overflow: hidden;
            height: 48px;
            width: 180px;
            position: absolute;
            right: 40px;
            &-item{
                font-size: 26px;
                text-align: center;
                flex: 1;
                color: #fff;
                height: 100%;
                line-height: 48px;
            }
            .selected{
                // font-size: ;
                color: #fff;
                background-color:#4b5c10;
                border-radius: 12px;
            }
        }
    }
    &-wrap {
        // background: orange;
        flex: 1;
        width: 100%;
        // height: 100%;
        display: flex;
        font-size: 28px;
        line-height: 1.2;
        height: calc(100% - 196px);
        // z-index: 400;
    }

    &-nav {
        width: 200px;
        // height: 100%;
        white-space: initial;
        background: #f3f3f3;
        text-align: center;
        position: relative;
        padding-bottom: calc(180px + env(safe-area-inset-bottom));
        &-item {
            color: #7b7b7b;
            padding: 24px;
            > view:first-child {
                margin-bottom: 24px;
            }
            position: relative;
        }
        .active {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          margin: 0 auto;
          border: 2rpx solid hsla(0, 86%, 44%, 0.8);
        }
        .line {
            border-bottom: 1rpx solid hsla(0, 0%, 48%, 0.2);
            margin: 0 auto;
            width: 80px;
            position: absolute;
            bottom: 0;
            left: 20px;
            right: 20px;
        }
    }

    &-main {
        // padding: 24px;
        padding-right: 0;
        padding-bottom: calc(180px + env(safe-area-inset-bottom));
    }
}
.total {
    background: #4b5c10;
    border-radius: 60px;
    box-shadow: 0 3px 8px 0 rgba(255, 255, 255, 0.4);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    position: fixed;
    bottom: 24px;
    margin: 24px;
    padding: 24px;
    box-sizing: border-box;
    left: 0;
    right: 0;
    z-index: 400 !important;
    color: #fff;
    &-left {
        display: flex;
        align-items: center;

        .icon {
            border-right: 1px solid rgba(255, 255, 255, 0.8);
            flex: 0.8;
            padding: 0 16px;
        }
        .text {
            margin-left: 16px;
        }
    }
}

.product-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 40px;
    margin: 24px;
    .product-img {
        width: 160px;
        height: 160px;
        border-radius: 24px;
        margin-right: 16px;
        position: relative;
        .product-sell-empty{
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.4);
            text-align: center;
            line-height: 40px;
            height: 40px;
            font-size: 20px;
            color: #ccc;
        }
    }
    
    .product-info {
        flex: 1;
        word-wrap: break-word;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        height: 160px;
        padding-right: 24px;
        > view:first-child {
            font-weight: 400;
        }
        &-content,
        &-quantity {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        &-spe{
            font-size: 20px;
            width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 40px;
            line-height: 40px;
            margin-left: 10px;
            white-space: pre;
            color: #999;
        }
        &-btn {
            // width: 30px;
            // height: 30px;
            // line-height: 30px;
            // border-radius: 50%;
            // border: 1px solid #4b5c10;
            // text-align: center;
            margin: 0 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            // background-color: #f1f1f1;
            border: 1px solid #4b5c10;
            color: #444;
            font-size: 24px;

        }
        &-btn1 {
            // margin: 0 12px;
            // display: flex;
            // justify-content: center;
            // align-items: center;
            text-align: center;
            width: 80px;
            height: 40px;
            line-height: 44px;
            // border-radius: 50%;
            // background-color: #f1f1f1;
            // border: 1px solid #4b5c10;
            background-color: @primaryColor;
            color: #fff;
            font-size: 24px;
            position: relative;
          
        }  
        .product-info-count{
            z-index: 10px;
            position: absolute;
            top: -15px;
            /* bottom: 20rpx; */
            right: -9px;
            border: 2px solid #4b5c10;
            border-radius: 50%;
            background-color: #e2c2bb;
            color: #333;
            min-width: 30px;
            text-align: center;
            height: 34px;
            font-size: 20px;
            line-height: 38px;
        }
    }
}

.cart-popup {
    z-index: 300;
    // padding-bottom: 160px;
    .btn{
        position: absolute;
        height: 44px;
        padding: 16px;
        background-color: @primaryColor;
        border-radius: 44px;
        line-height: 44px;
        font-size: 28px;
        text-align: center;
        color: #fff;
        bottom: -38px;
        right: 40px;
    }
    .good-name{
        position: absolute;
        left: 0;
        bottom: 0;
        padding-bottom: 38px;
        height: 78px;
        font-size: 36px;
        line-height: 40px;
        color: #fff;
        padding-left: 32px;
    }
    &-header {
        padding: 40px 24px;
        box-sizing: border-box;
        // height: 200px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .count {
            color: #333;
            font-weight: 600;
        }
        .clear-all {
            color: #999;
            float: right;
            font-size: 24rpx;
            font-weight: 400;
            display: flex;
            align-items: center;
        }
    }
    .popup-product-item {
        padding: 0 24px 24px;
        &:last-child {
            margin-bottom: 200px;
        }
        .product-img {
            border-radius: 50%;
            width: 100px;
            height: 100px;
        }
        .product-info {
            height: 100px;
            > view:first-child {
                font-weight: normal;
            }
            &-content {
                > view:first-child {
                    font-weight: 500;
                }
            }
        }
    }
}
// .van-native-button{
//     background-color: @primaryColor;
// }
.van-button__text{
    color: @primaryColor;
}