.goods-attribute-list{
    .good-img{
        width: 160px;
        height: 160px;
        margin-right: 20px;
        border-radius: 20px;
    }
    .good-name{font-size: 30px;}
    
    .selected{
        padding: 20px;
        position: relative;
        // background-color: rgb(231, 160, 160);
        border-color: rgb(230, 116, 116);
        // bo
        border: 2px solid rgb(230, 116, 116);
        color: rgb(230, 116, 116);
        border-radius: 10px;
        margin-right: 20px;
        margin-top: 10px;
        position: relative;;
        // color: aquamarine;
    }
    .item-disabled{
        opacity: 0.5;
        color: #666;
    }
    .default{
        padding: 20px;
        position: relative;
        border: 2px solid  rgb(248, 246, 246);
        border-radius: 10px;
        margin-right: 20px;
        margin-top: 10px;
        // color: rgb(230, 116, 116);
        background-color: rgb(248, 246, 246);
        position: relative;
    }
    .tag-add-price{
        // position: absolute;
        // right: 0;
        // top: 0;
        font-size: 24px;
        margin-left: 10px;
    }

    .good-num-cell{
        width: 100%;
        margin-top: 20px;
        margin-bottom: 20px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        .product-info-btn{
            margin: 0 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            // background-color: #f1f1f1;
            border: 1px solid #4b5c10;
            color: #444;
            font-size: 24px;
        }
    }
    .attr-item{
        margin-top: 20px;
        .attr-item-title{
            font-size: 24px;
            margin-bottom: 10px;
        }
        .attr-item-vals{
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            align-items: center;
        }
    }
    .popup-bottom{
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 10px;
        .popup-bottom-price{
            font-size: 30px;
        }
        .popup-bottom-btn{
            margin: 0;
            background-color: #4b5c10;
            color: #fff;
            .van-button__text{
                color: #fff;
            }
        }
    }
}