/* eslint-disable */
import react, { useState } from "react";
import { View, Image, ScrollView } from "@tarojs/components";
import { Empty, Icon, Popup } from "@antmjs/vantui";
import { MiniObject } from "src/type/common";
import api from "@/util/api";

interface Props {
    show: boolean;
    data: any;
    totalQuantity: any,
    onClose: (allClear?: boolean) => void;
    handleDecrease: (id: number, item: MiniObject, type: string) => void;
    handleIncrease: (id: number, item?: MiniObject) => void;
}
function CartPopup({ data, show, onClose, totalQuantity, handleDecrease, handleIncrease }: Props) {
    const clearCarts = () => {
        // console.log('clearCarts');
        api.clearCarts({ data: {}, method: 'post' }).then(() => {
            onClose(true);
        })
    }
    return (
        <View className="cart-popup">
            <Popup show={show} position="bottom" onClose={onClose} round={true} safeAreaInsetTop={true}
                style={{
                    paddingBottom: 'calc(100px + env(safe-area-inset-bottom))'
                }}
            >
                <View className="cart-popup-header">
                    <View className="count">已选商品({totalQuantity})</View>
                    <View className="clear-all" onClick={() => {
                        clearCarts()
                    }}>
                        <Icon name="delete-o" className="icon" size="20px" />
                        <View>清空购物车</View>
                    </View>
                </View>
                <ScrollView
                    scroll-y
                    scroll-with-animation
                    style={{ height: '400px' }}
                >
                    {data?.map((item: any) => (
                        <View className="product-item popup-product-item" key={item.id}>
                            <Image src={item.picUrl} className="product-img" />
                            <View className="product-info">
                                <View>{item.goodsName}</View>
                                <View className="product-info-content">
                                    <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
                                        {
                                            item.discountFlag ?
                                                <View style={{ display: 'flex', flexDirection: 'row' }}>
                                                    <View style={{ textDecoration: 'line-through' }}>￥{item.price}</View>
                                                    <View style={{ marginLeft: '8px', color: 'red' }}>¥{item.discountPrice}</View>
                                                </View>
                                                :
                                                <View>￥{item.price}</View>

                                        }
                                        <View className="product-info-spe">{item.specifications}</View>
                                    </View>

                                    <View className="product-info-quantity">
                                        <View onClick={() => handleDecrease(item.id, item, 'goodsId')} className="product-info-btn">
                                            -
                                        </View>
                                        <View>{item.number}</View>
                                        <View onClick={() => handleIncrease(item.id, item)} className="product-info-btn">
                                            +
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </View>
                    ))}
                    {
                        data.length < 1 && <Empty description='暂无商品' />
                    }
                </ScrollView>

            </Popup>
        </View>
    );
}

export default CartPopup;
