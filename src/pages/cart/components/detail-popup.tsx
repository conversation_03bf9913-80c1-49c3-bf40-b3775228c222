/* eslint-disable */
import react, { useState } from "react";
import { View, Image } from "@tarojs/components";
import { Empty, Icon, Popup } from "@antmjs/vantui";
import { MiniObject } from "src/type/common";
import api from "@/util/api";

interface Props {
    show: boolean;
    data: any;
    // totalQuantity: any,
    onClose: (allClear?: boolean) => void;
    // handleDecrease: (id: number, item: MiniObject, type: string) => void;
    handleIncrease: (id: number, item?: MiniObject) => void;
}
function DetailPopup({ data, show, onClose, handleIncrease }: Props) {
    console.log("data:", data)
    return (
        <View className="cart-popup">
            <Popup show={show} position="bottom" onClose={onClose} round={true} safeAreaInsetTop={true}
                style={{
                    paddingBottom: 'calc(100px + env(safe-area-inset-bottom))'
                }}
            >
                <View style={{ width: '100vw', height: '100vw', position: 'relative', marginBottom: "100px" }} >
                    <Image src={data?.picUrl} mode="aspectFill" style={{ width: '100vw', height: '100vw' }} />
                    <View className="good-name">{data?.name}</View>
                    <View className="btn" onClick={() => {
                        handleIncrease(data?.id, data)
                    }}>¥{(data?.discountFlag ? data?.discountPrice : data?.retailPrice) || 0} 加入购物车</View>
                </View>
            </Popup>
        </View>
    );
}

export default DetailPopup;
