import react, { useEffect, useState } from "react";
import { View, Image, Text, ScrollView } from "@tarojs/components";
import { Empty, Icon, Popup, Button } from "@antmjs/vantui";
import { MiniObject } from "src/type/common";
import api from "@/util/api";
import './goods-attribute-list.less'
import MiniToast from "@/util/toast";
export default function ({
    goodsItem,
    // reload
    show,
    closeFn
}) {
    const [goodsAttributes, setgoodsAttributes] = useState([])
    const [checkedCount, setcheckedCount] = useState(1);
    const [totalPrice, settotalPrice] = useState(0.00)
    const selectAttrValues = (attrIndex, attrValueIndex, val) => {
        goodsAttributes[attrIndex].goodsSpecifications.map((item, index) => {
            if (val) {
                if (attrValueIndex !== index) {
                    item.selected = !val;
                } else {
                    item.selected = val;
                }
            } else {
                item.selected = val;
            }
        })
        // goodsAttributes[attrIndex].goodsSpecifications[attrValueIndex].selected = val;
        changePrice(checkedCount)
        setgoodsAttributes([...goodsAttributes])
    }

    const changePrice = (num) => {
        let resultNum = num * (goodsItem.discountFlag ? goodsItem.discountPrice : goodsItem.retailPrice);
        goodsAttributes.map((item) => {
            item.goodsSpecifications.map((speItem) => {

                if (speItem
                    .selected && speItem.price > 0 || speItem.price < 0) {
                    // speItem.price = -0.01
                    resultNum += +((num * Number(speItem.price)).toFixed(2))
                }
            })
        })
        console.log(resultNum)
        settotalPrice(resultNum);
    }

    const addCart = () => {
        // goodsAttributes.
        for (let arrIndex = 0; arrIndex < goodsAttributes.length; arrIndex++) {
            const item = goodsAttributes[arrIndex];
            // if (item.required)
            // 
            let bol = false;
            for (let valIndex = 0; valIndex < item.goodsSpecifications.length; valIndex++) {
                if (item.goodsSpecifications[valIndex].selected) {
                    bol = true;
                    break
                }
            }
            if (item.required && bol === false) {

                // break;
                MiniToast.info(`${item.value} 是必选的`)
                return
            }
        }
        api.cartAdd({
            data: {
                goodsId: goodsItem.id,
                number: checkedCount,
                goodsAttributes: goodsAttributes,

            },
            method: 'post',
        }).then(res => {
            closeFn(true, goodsItem.id, (goodsItem.checkedCount || 0) + checkedCount);
            // updatebase(productId, number)
            // getCarts();
        })
    }


    useEffect(() => {
        if (show) {
            let resultPrice = 1 * (goodsItem.discountFlag ? goodsItem.discountPrice : goodsItem.retailPrice);
            goodsItem.goodsAttributes.map((item) => {
                let min = -1;
                item.goodsSpecifications.map((obj, index) => {
                    console.log('item:', item)
                    if (item.required && obj.enable && min < 0) {
                        console.log("item:", item)
                        min = index;
                        obj.selected = true;
                        resultPrice += +((Number(obj.price)).toFixed(2))
                    } else {
                        obj.selected = false
                    }
                })
            })
            setgoodsAttributes(goodsItem.goodsAttributes || []);
            // setcheckedCount(goodsItem?.checkedCount);
            // settotalPrice((goodsItem.retailPrice).toFixed(2))
            // changePrice(1)
            settotalPrice(resultPrice)
        } else {
            setgoodsAttributes([]);
            setcheckedCount(1);
            settotalPrice(0.00)
        }
    }, [show])
    return (
        <Popup show={show} onClose={() => { closeFn && closeFn() }}
            round
        >
            <View
                style={{
                    width: '300px', height: '400px',
                    display: 'flex',
                    padding: '40px',
                    flexDirection: 'column',
                    alignItems: 'center',
                    // justifyContent: 'center',
                    borderRadius: '64px'
                }}
                className="goods-attribute-list"
            >
                <View style={{
                    display: 'flex',
                    flexDirection: 'row',
                    // alignItems: 'center'
                    width: '100%',

                }}>
                    <Image src={goodsItem?.picUrl} className="good-img" mode="scaleToFill" />
                    <Text className="good-name">{goodsItem?.name}</Text>
                </View>
                <View className="good-num-cell">
                    <Text>数量</Text>
                    <View style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                    }}>
                        {checkedCount > 0 && (
                            <View onClick={() => {
                                // handleDecrease(goodsItem.id, goodsItem, 'productId')
                                setcheckedCount(checkedCount - 1);
                                changePrice(checkedCount - 1)
                            }} className="product-info-btn">
                                -
                            </View>
                        )}
                        {checkedCount > 0 && <View>{checkedCount}</View>}
                        <View onClick={() => {
                            // addIncrease(goodsItem.id, goodsItem)
                            setcheckedCount(checkedCount + 1);
                            changePrice(checkedCount + 1)
                        }} className="product-info-btn">
                            +
                        </View>
                    </View>
                </View>

                <ScrollView

                    style={{
                        width: '100%',
                        height: '200px'
                    }}
                    scroll-y scroll-with-animation
                >
                    <View>
                        {
                            goodsAttributes.map((item, index) => {
                                return (
                                    <View className="attr-item">

                                        <View className="attr-item-title">
                                            {item.value}
                                            {item.required ? <Text style={{ color: 'red' }}>(*必选)</Text> : null}
                                        </View>
                                        <View className="attr-item-vals">

                                            {
                                                item?.goodsSpecifications.map((speItem, speIndex) => {
                                                    return (
                                                        <View onClick={() => {
                                                            if (!speItem.enable) return;
                                                            selectAttrValues(index, speIndex, !speItem.selected)
                                                            changePrice(checkedCount)
                                                        }}
                                                            className={`${speItem.selected ? 'selected' : 'default'} ${!speItem.enable ? 'item-disabled' : ''}`}
                                                        >
                                                            {speItem.value}
                                                            {/* {speItem.price > 0 || speItem.price < 0 ?
                                                                <Text className="tag-add-price">¥{speItem.price}</Text>
                                                                : null} */}
                                                        </View>
                                                    )
                                                })
                                            }
                                        </View>
                                    </View>)
                            })
                        }
                    </View>
                </ScrollView>
                <View className="popup-bottom">
                    <Text className="popup-bottom-price">¥{totalPrice}</Text>
                    <Button
                        className="popup-bottom-btn"
                        icon="shopping-cart-o" type="primary"
                        onClick={() => { addCart() }}
                    >
                        加入购物车
                    </Button>
                </View>
            </View>

        </Popup >
    )
}