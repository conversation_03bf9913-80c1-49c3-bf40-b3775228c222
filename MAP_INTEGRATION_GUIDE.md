# 地图集成功能说明

## 🗺️ 地图功能概述

已成功将地图组件集成到店铺选择页面中，完全按照您提供的示例图实现：

### ✨ 主要特性

1. **地图背景显示**
   - 地图占据页面上半部分（50%高度）
   - 显示所有店铺的位置标记
   - 支持用户当前位置显示

2. **交互功能**
   - 点击地图标记可直接选择店铺
   - 支持地图缩放和拖拽
   - 店铺标记显示名称气泡

3. **动态控制**
   - 可以收起/显示地图
   - 收起后显示"显示地图"按钮
   - 流畅的动画过渡效果

4. **自适应布局**
   - 有地图时：上半部分地图，下半部分列表
   - 无地图时：全屏显示店铺列表
   - 响应式设计，适配不同屏幕尺寸

## 🎯 实现细节

### 页面结构
```
┌─────────────────────┐
│     地图背景区域      │  ← 50vh 高度
│   (可收起/显示)      │
├─────────────────────┤
│     内容区域        │  ← 50vh 高度
│   ┌─────────────┐   │
│   │   标题栏    │   │
│   ├─────────────┤   │
│   │   搜索框    │   │
│   ├─────────────┤   │
│   │  店铺列表   │   │
│   │     ...     │   │
│   └─────────────┘   │
└─────────────────────┘
```

### 关键功能

1. **位置获取**
   ```typescript
   // 自动获取用户位置
   Taro.getLocation({
       type: 'gcj02',
       success: (res) => {
           setUserLocation({
               latitude: res.latitude,
               longitude: res.longitude
           });
       }
   });
   ```

2. **距离计算**
   ```typescript
   // 计算用户到店铺的距离
   const calculateDistance = (lat1, lng1, lat2, lng2) => {
       // 使用球面距离公式计算
   };
   ```

3. **地图标记**
   ```typescript
   markers={shopList.map(shop => ({
       id: shop.id,
       latitude: shop.latitude,
       longitude: shop.longitude,
       title: shop.shopName,
       callout: {
           content: shop.shopName,
           // 样式配置...
       }
   }))}
   ```

## 🎨 样式特点

### 地图区域
- 固定在页面顶部
- 半透明控制按钮
- 毛玻璃效果背景
- 平滑的显示/隐藏动画

### 内容区域
- 圆角顶部设计
- 阴影效果
- 动态高度调整
- 流畅的过渡动画

### 响应式适配
- 小屏幕：地图40vh，内容60vh
- 大屏幕：地图55vh，内容45vh
- 超小屏幕：优化布局比例

## 🔧 配置要求

### 小程序权限配置
在 `app.json` 中添加：
```json
{
  "permission": {
    "scope.userLocation": {
      "desc": "您的位置信息将用于计算距离"
    }
  }
}
```

### API接口
确保后端提供店铺列表接口，包含：
```typescript
interface ShopItem {
    id: number;
    shopName: string;
    address: string;
    latitude: number;    // 纬度
    longitude: number;   // 经度
    businessHours: string;
    phone?: string;
}
```

## 🚀 使用方法

1. **直接访问页面**
   ```typescript
   MiniNavigateTo({
       url: '/pages/shop/select-shop/index'
   });
   ```

2. **通过组件跳转**
   ```tsx
   <ShopSelector />
   ```

## 🎯 用户体验

1. **首次进入**
   - 自动获取位置权限
   - 显示地图和店铺标记
   - 计算并显示距离

2. **地图交互**
   - 点击标记选择店铺
   - 拖拽查看更多区域
   - 缩放查看详细位置

3. **列表交互**
   - 按距离排序显示
   - 支持搜索过滤
   - 一键拨打电话
   - 一键地图导航

4. **状态切换**
   - 收起地图：更多列表空间
   - 显示地图：直观位置信息
   - 流畅的动画过渡

## 📱 测试建议

1. **功能测试**
   - 测试地图显示和隐藏
   - 测试标记点击选择
   - 测试位置权限获取
   - 测试距离计算准确性

2. **兼容性测试**
   - 不同屏幕尺寸适配
   - 不同微信版本兼容
   - 网络异常处理

3. **性能测试**
   - 地图加载速度
   - 动画流畅度
   - 内存使用情况

这个实现完全符合您提供的UI设计，提供了完整的地图集成功能和优秀的用户体验！
