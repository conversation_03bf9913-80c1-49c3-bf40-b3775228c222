# 移动端设计规范调整

## 📱 设计原则

按照微信小程序和移动端设计规范，重新调整了字体大小和间距，确保在移动设备上有最佳的阅读体验和交互体验。

## 🔤 字体层次规范

### 主要字体大小
- **页面主标题**: 18px (重要页面标题)
- **页面副标题**: 13px (说明文字)
- **店铺名称**: 17px (列表项主要信息)
- **详细信息**: 14px (地址、营业时间等)
- **辅助信息**: 12-13px (距离、按钮文字等)
- **搜索框**: 16px (输入文字)

### 字体层次说明
```
18px - 页面主标题 (最重要)
  ↓
17px - 内容主标题 (店铺名称)
  ↓
16px - 输入框文字
  ↓
14px - 正文内容 (地址、时间)
  ↓
13px - 副标题、距离数字
  ↓
12px - 辅助文字、按钮文字
```

## 📏 间距规范

### 容器间距
- **页面边距**: 16px (标准移动端边距)
- **卡片内边距**: 16px (列表项内部间距)
- **搜索框内边距**: 10px 12px (输入框内部间距)
- **标题区域内边距**: 16px (顶部标题区域)

### 元素间距
- **标题间距**: 4px (主副标题之间)
- **信息行间距**: 4px (地址、时间等信息之间)
- **按钮间距**: 12px (操作按钮之间)
- **图标文字间距**: 4-6px (图标和文字之间)

### 组件尺寸
- **搜索框最小高度**: 40px (符合触摸标准)
- **列表项最小高度**: 80px (合理的内容展示空间)
- **操作按钮**: 44px × 44px (符合触摸标准)
- **地图控制按钮**: 最小宽度 100px

## 🎯 移动端最佳实践

### 1. 触摸友好性
- ✅ 所有可点击元素最小 44px × 44px
- ✅ 按钮间距至少 12px，避免误触
- ✅ 输入框高度至少 40px

### 2. 可读性优化
- ✅ 正文字体不小于 14px
- ✅ 重要信息使用 16-18px
- ✅ 辅助信息使用 12-13px
- ✅ 合理的行高和字间距

### 3. 视觉层次
- ✅ 通过字体大小建立信息层次
- ✅ 重要信息突出显示
- ✅ 辅助信息适当弱化
- ✅ 合理的间距分组

### 4. 响应式适配
- ✅ 适配不同屏幕尺寸
- ✅ 保持一致的视觉比例
- ✅ 优化小屏幕显示效果

## 📊 具体调整对比

| 元素 | 调整前 | 调整后 | 说明 |
|------|--------|--------|------|
| 页面主标题 | 44px | 18px | 符合移动端标题规范 |
| 页面副标题 | 30px | 13px | 适合说明文字 |
| 店铺名称 | 36px | 17px | 突出但不过大 |
| 地址信息 | 28px | 14px | 易读的正文大小 |
| 距离信息 | 28px | 13px | 合适的辅助信息 |
| 搜索框 | 32px | 16px | 标准输入框字体 |
| 按钮文字 | 28px | 12-14px | 清晰的按钮标签 |

## 🎨 视觉效果改进

### 信息密度优化
- 减少了过大的字体造成的信息密度过低
- 提高了屏幕空间利用率
- 保持了良好的可读性

### 交互体验提升
- 按钮和输入框符合触摸标准
- 合理的间距避免误操作
- 清晰的视觉层次引导用户

### 一致性保证
- 统一的字体大小规范
- 一致的间距系统
- 符合微信小程序设计规范

## 📱 适配效果

### 小屏幕设备 (iPhone SE)
- 信息显示完整，不会被截断
- 按钮大小适中，易于点击
- 文字清晰可读

### 标准屏幕设备 (iPhone 12)
- 视觉层次清晰
- 信息密度合理
- 交互体验流畅

### 大屏幕设备 (iPhone 12 Pro Max)
- 充分利用屏幕空间
- 保持视觉比例协调
- 避免元素过小或过大

## ✅ 符合的设计规范

1. **微信小程序设计指南**
2. **iOS Human Interface Guidelines**
3. **Material Design Guidelines**
4. **移动端可访问性标准**

这些调整确保了在各种移动设备上都能提供优秀的用户体验，符合现代移动应用的设计标准。
