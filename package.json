{"name": "mini-shop", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "mobx", "typescript": true, "css": "less"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "license": "MIT", "dependencies": {"@antmjs/vantui": "3.2.0", "@babel/runtime": "7.7.7", "@tarojs/components": "3.6.15", "@tarojs/helper": "3.6.15", "@tarojs/plugin-framework-react": "3.6.15", "@tarojs/plugin-platform-alipay": "3.6.15", "@tarojs/plugin-platform-h5": "3.6.15", "@tarojs/plugin-platform-jd": "3.6.15", "@tarojs/plugin-platform-qq": "3.6.15", "@tarojs/plugin-platform-swan": "3.6.15", "@tarojs/plugin-platform-tt": "3.6.15", "@tarojs/plugin-platform-weapp": "3.6.15", "@tarojs/react": "3.6.15", "@tarojs/runtime": "3.6.15", "@tarojs/shared": "3.6.15", "@tarojs/taro": "3.6.15", "babel-plugin-import": "1.13.8", "dayjs": "1.11.9", "mobx": "6.10.2", "mobx-react": "9.1.1", "mobx-react-lite": "4.0.4", "react": "18.0.0", "react-dom": "18.0.0", "taro-code": "^4.0.1", "taro-code-creator": "^1.0.6"}, "devDependencies": {"@babel/core": "7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "0.5.5", "@tarojs/cli": "3.6.15", "@tarojs/plugin-mini-ci": "3.6.18", "@tarojs/taro-loader": "3.6.15", "@tarojs/webpack5-runner": "3.6.15", "@types/node": "18.15.11", "@types/react": "18.0.0", "@types/webpack-env": "1.13.6", "@typescript-eslint/eslint-plugin": "5.20.0", "@typescript-eslint/parser": "5.20.0", "axios": "1.5.1", "babel-preset-taro": "3.6.15", "eslint": "^8.12.0", "eslint-config-taro": "3.6.15", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "husky": "8.0.3", "postcss": "8.4.18", "prettier": "2.8.8", "pretty-quick": "3.1.3", "react-refresh": "0.11.0", "stylelint": "9.3.0", "ts-node": "10.9.1", "typescript": "^4.1.0", "webpack": "5.78.0"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}